# Phase 2: Audio Feature Extraction and Temporal Alignment - Request for Proposal (RFP)
## Self-Contained Implementation Specification

## 1. Phase Overview and System Context

### 1.1 Project Objective
Implement a **TJA rhythm chart generation system** that creates high-quality Taiko no Tatsufin charts from audio input using deep learning. This system analyzes audio files and generates appropriate note sequences for difficulty levels 8-10 (Oni/Edit courses).

### 1.2 Phase 2 Purpose and Role
Phase 2 transforms validated audio files into rich, time-aligned feature representations suitable for machine learning. This phase extracts multi-scale audio features that capture rhythmic patterns, harmonic content, and temporal dynamics essential for generating high-difficulty TJA charts. The output provides the audio foundation for the neural network training pipeline.

### 1.3 Implementation Status and Performance
**Status**: 🔄 **READY** - Components organized, ready for implementation

**Target Performance Metrics**:
- **Feature Dimensions**: [T, 201] per audio file
- **Processing Speed**: Real-time capable
- **GPU Utilization**: 70-88% sustained
- **Memory Efficiency**: <4GB VRAM usage

**Key Implementation Files**:
- `src/phase_2/controller.py` - Main phase controller
- `src/phase_2/feature_extractor.py` - Multi-scale feature extraction
- `src/phase_2/spectral_processor.py` - Spectral analysis
- `src/phase_2/rhythmic_processor.py` - Rhythmic pattern extraction
- `src/phase_2/temporal_aligner.py` - Temporal alignment

### 1.4 Hardware Environment (Verified)
**CRITICAL**: All implementations must be optimized for the verified hardware environment:

```python
HARDWARE_SPECS = {
    "gpu": {
        "model": "NVIDIA GeForce RTX 3070",
        "vram_gb": 8.0,
        "cuda_version": "12.1",
        "pytorch_version": "2.5.1+cu121",
        "memory_constraint": "6.8GB usable (safety margin)"
    },
    "cpu": {
        "physical_cores": 8,
        "logical_cores": 16,
        "recommended_workers": 12,  # Reserve 4 cores for system
        "optimization_target": "parallel_audio_processing"
    },
    "memory": {
        "total_ram_gb": 31.8,
        "available_ram_gb": 28.0,
        "recommended_cache_gb": 16,
        "processing_buffer_gb": 8
    }
}
```

### 1.5 Phase Dependencies and Data Flow
- **Previous Phase**: Phase 1 (Data Analysis and Preprocessing) - requires validated catalog
- **Next Phase**: Phase 3 (TJA Sequence Processing and Temporal Encoding)
- **Input Contract**: `data/processed/catalog.json` with validated schema from Phase 1
- **Output Contract**: Time-aligned audio features `[T, 201]` for Phase 3 consumption
- **Data Flow**: Validated audio files → Multi-scale feature extraction → Time-aligned features → Phase 3 input

## 2. Phase 1 Input Contract (Validated Schema)

### 2.1 Required Input from Phase 1
**Primary Input**: `data/processed/catalog.json` with validated schema:
```json
{
  "phase_metadata": {
    "phase": "1",
    "version": "1.0.0",
    "hardware_optimized": true,
    "processing_timestamp": "2024-07-24T10:00:00Z"
  },
  "songs": [
    {
      "song_id": "unique_identifier",
      "audio_path": "resolved/path/to/audio.ogg",
      "notation_metadata": {
        "base_bpm": 120.0,
        "offset": -1.5,
        "duration_seconds": 180.5,
        "sample_rate": 44100
      },
      "validation_status": "valid",
      "phase_2_ready": true
    }
  ],
  "validation_summary": {
    "overall_success_rate": 0.975,
    "training_readiness": 0.92
  }
}
```

### 2.2 Input Validation Requirements
- **Data Catalog**: Validated `data/processed/catalog.json` from Phase 1
- **Audio Files**: .ogg files with confirmed quality metrics and verified paths
- **TJA Metadata**: BPM, offset, timing commands for synchronization
- **Target Sample Rate**: 44.1kHz (standardized from Phase 1)
- **Hardware Compatibility**: Files must be processable within RTX 3070 constraints

## 3. Phase 2 Output Contract (Phase 3 Input Specification)

### 3.1 Primary Output: Audio Features Tensor
**Schema Definition for Phase 3 Consumption**:
```python
# CORRECTED: Per-song feature tensor shape: [time_frames, 201_channels]
# Based on actual hardware capabilities (RTX 3070, 32GB RAM)
audio_features = {
    "spectral_features": torch.tensor([T, 128]),      # Mel-spectrogram (128 bins)
    "mfcc_features": torch.tensor([T, 13]),           # MFCC coefficients
    "chroma_features": torch.tensor([T, 12]),         # Chroma (12 pitch classes)
    "rhythmic_features": torch.tensor([T, 32]),       # Onset, tempo, beat tracking
    "temporal_features": torch.tensor([T, 16]),       # RMS, spectral statistics
    "combined_features": torch.tensor([T, 201]),      # Total: 128+13+12+32+16=201
    "timing_grid": torch.tensor([T, 4]),              # BPM-aligned grid (separate)
    "metadata": {
        "frame_rate": 50.0,                           # Features per second
        "hop_length": 882,                            # 44100/50 = 882 samples per frame
        "sample_rate": 44100,                         # Audio sample rate
        "feature_dimensions": {
            "mel_spectrogram": 128,
            "mfcc": 13,
            "chroma": 12,
            "rhythmic": 32,
            "temporal": 16,
            "total": 201  # CORRECTED from previous 240
        },
        "bpm_sequence": torch.tensor([T]),            # Time-varying BPM
        "beat_positions": torch.tensor([num_beats]),  # Beat timestamps
        "measure_positions": torch.tensor([num_measures]),
        "hardware_optimized": True,
        "processing_config": {
            "batch_size": 4,                          # Optimized for 32GB RAM
            "gpu_acceleration": True,                 # RTX 3070 utilization
            "parallel_workers": 12                   # 16 logical cores - 4 reserved
        }
    }
}

# Hardware-optimized memory allocation for RTX 3070 system
PHASE_2_MEMORY_ALLOCATION = {
    "per_song_features_mb": 50,    # ~50MB per 3-minute song at 201 channels
    "batch_processing_gb": 2,      # 2GB for 4-song batches
    "gpu_processing_gb": 1.5,      # GPU memory for feature extraction
    "total_cache_gb": 16,          # Use 16GB of 32GB RAM for caching
    "safety_margin_gb": 4,         # Reserve 4GB for system operations
    "phase_3_output_gb": 4         # Reserve 4GB for Phase 3 handoff
}

# Resource utilization targets for hardware optimization
PHASE_2_RESOURCE_EFFICIENCY_TARGETS = {
    "gpu_resource_utilization": {
        "sustained_gpu_utilization": 0.80,      # 80% sustained GPU utilization
        "vram_efficiency": 0.90,                # 90% VRAM efficiency
        "compute_unit_occupancy": 0.85,         # 85% compute unit occupancy
        "gpu_memory_bandwidth_utilization": 0.88, # 88% memory bandwidth usage
        "tensor_core_utilization": 0.75         # 75% tensor core utilization
    },
    "memory_resource_efficiency": {
        "memory_utilization": 0.85,             # 85% memory efficiency
        "cache_hit_ratio": 0.92,                # 92% cache hit ratio
        "memory_bandwidth_utilization": 0.82,   # 82% memory bandwidth usage
        "buffer_efficiency": 0.88,              # 88% buffer efficiency
        "memory_fragmentation": 0.08            # <8% memory fragmentation
    },
    "feature_quality": {
        "temporal_alignment_accuracy": 0.95,    # 95% temporal alignment accuracy
        "feature_extraction_success": 0.98,     # 98% feature extraction success
        "synchronization_accuracy": 0.97        # 97% synchronization accuracy
    }
}
```

#### 2.2.2 Directory Structure
```
data/processed/audio_features/
├── spectral/
│   ├── {song_id}_mel_spec.pt
│   ├── {song_id}_mfcc.pt
│   └── {song_id}_chroma.pt
├── rhythmic/
│   ├── {song_id}_onset.pt
│   ├── {song_id}_tempo.pt
│   └── {song_id}_beat_track.pt
├── temporal_grids/
│   ├── {song_id}_timing_grid.pt
│   └── {song_id}_bpm_sequence.pt
├── synchronization/
│   ├── {song_id}_sync_metadata.json
│   └── alignment_validation.json
└── feature_statistics.json
```

### 2.3 Core Feature Extraction Components

#### 2.3.1 Spectral Feature Pipeline
```python
def extract_spectral_features(audio, sr=44100, frame_rate=50):
    """Extract spectral features optimized for rhythm detection"""
    hop_length = sr // frame_rate  # 882 samples at 44.1kHz
    
    # Mel-spectrogram (128 bins, 80Hz-8kHz for drum content)
    mel_spec = librosa.feature.melspectrogram(
        y=audio, sr=sr, n_mels=128, hop_length=hop_length,
        fmin=80, fmax=8000, power=2.0
    )
    mel_spec_db = librosa.power_to_db(mel_spec, ref=np.max)
    
    # MFCC (13 coefficients for timbral content)
    mfcc = librosa.feature.mfcc(
        y=audio, sr=sr, n_mfcc=13, hop_length=hop_length
    )
    
    # Chroma features (12 bins for harmonic content)
    chroma = librosa.feature.chroma_stft(
        y=audio, sr=sr, hop_length=hop_length, n_chroma=12
    )
    
    return {
        "mel_spectrogram": torch.tensor(mel_spec_db.T, dtype=torch.float32),
        "mfcc": torch.tensor(mfcc.T, dtype=torch.float32),
        "chroma": torch.tensor(chroma.T, dtype=torch.float32)
    }
```

#### 2.3.2 Rhythmic Feature Pipeline
```python
def extract_rhythmic_features(audio, sr=44100, frame_rate=50):
    """Extract rhythm-specific features for note placement prediction"""
    hop_length = sr // frame_rate
    
    # Onset detection with multiple methods
    onset_strength = librosa.onset.onset_strength(
        y=audio, sr=sr, hop_length=hop_length, aggregate=np.median
    )
    
    # Spectral flux for transient detection
    stft = librosa.stft(audio, hop_length=hop_length)
    spectral_flux = np.diff(np.abs(stft), axis=1)
    spectral_flux = np.pad(spectral_flux, ((0, 0), (1, 0)), mode='constant')
    
    # Tempo and beat tracking
    tempo, beats = librosa.beat.beat_track(
        y=audio, sr=sr, hop_length=hop_length, units='time'
    )
    
    # Convert beats to frame indices
    beat_frames = librosa.time_to_frames(beats, sr=sr, hop_length=hop_length)
    
    return {
        "onset_strength": torch.tensor(onset_strength, dtype=torch.float32),
        "spectral_flux": torch.tensor(np.mean(spectral_flux, axis=0), dtype=torch.float32),
        "tempo": tempo,
        "beat_positions": torch.tensor(beat_frames, dtype=torch.long)
    }
```

#### 2.3.3 Temporal Alignment System
```python
def create_timing_grid(audio_duration, bpm_sequence, offset, frame_rate=50):
    """Create BPM-aligned temporal grid for feature-to-note alignment"""
    total_frames = int(audio_duration * frame_rate)
    time_axis = np.linspace(0, audio_duration, total_frames)
    
    # Account for TJA offset (chart delay relative to audio)
    adjusted_time = time_axis - offset
    
    # Create beat-aligned grid
    beat_grid = np.zeros(total_frames)
    measure_grid = np.zeros(total_frames)
    
    current_beat_time = 0
    for frame_idx, time in enumerate(adjusted_time):
        if time >= 0:  # Only after offset
            # Calculate current BPM (handle BPM changes)
            current_bpm = interpolate_bpm(time, bpm_sequence)
            beat_duration = 60.0 / current_bpm
            
            # Mark beat positions
            beat_phase = (time % beat_duration) / beat_duration
            beat_grid[frame_idx] = 1.0 - beat_phase  # Proximity to beat
            
            # Mark measure positions (4/4 time signature assumed)
            measure_phase = (time % (beat_duration * 4)) / (beat_duration * 4)
            measure_grid[frame_idx] = 1.0 - measure_phase
    
    return {
        "beat_grid": torch.tensor(beat_grid, dtype=torch.float32),
        "measure_grid": torch.tensor(measure_grid, dtype=torch.float32),
        "time_axis": torch.tensor(time_axis, dtype=torch.float32),
        "bpm_sequence": torch.tensor(bpm_sequence, dtype=torch.float32)
    }
```

## 3. Output Path Specifications

### 3.1 Standardized Directory Structure
Phase 2 outputs are organized within the standardized `data/phase_2/` directory structure:

```
data/phase_2/
├── outputs/                   # Primary phase outputs
│   ├── audio_features/       # Feature tensors [T, 201] per audio file
│   │   ├── spectral_features/    # Mel spectrograms and spectral features
│   │   ├── rhythmic_features/    # Onset detection and rhythm analysis
│   │   ├── harmonic_features/    # Harmonic content and pitch features
│   │   └── temporal_features/    # Temporal dynamics and envelope
│   ├── feature_metadata.json    # Feature extraction parameters
│   ├── alignment_data.json      # Temporal alignment information
│   └── phase3_catalog.json      # Enhanced catalog for Phase 3
├── metadata/                 # Processing metadata
│   ├── extraction_statistics.json
│   ├── processing_errors.json
│   └── audio_quality_metrics.json
├── validation/               # Validation results
│   ├── feature_validation_report.json
│   ├── temporal_alignment_validation.json
│   └── quality_assessment.json
├── logs/                     # Phase-specific logs
│   ├── phase2_processing.log
│   ├── feature_extraction.log
│   └── gpu_utilization.log
└── temp/                     # Temporary processing files
    ├── batch_processing/
    └── intermediate_features/
```

### 3.2 Output Schema Specification
Phase 2 implements the `Phase2Output` schema extending the base `StandardizedOutput`:

```python
@dataclass
class Phase2Output(StandardizedOutput):
    phase_number: int = 2
    phase_name: str = "Audio Feature Extraction"

    # Phase-specific outputs
    feature_tensor_shape: List[int]  # [T, 201]
    sample_rate: int                 # Audio sample rate (44100)
    duration_seconds: float          # Total processing duration
    feature_types: List[str]         # Types of extracted features
    temporal_resolution_ms: float    # Temporal resolution in milliseconds

    # File paths
    audio_features_dir: str = "data/phase_2/outputs/audio_features/"
    feature_metadata_path: str = "data/phase_2/outputs/feature_metadata.json"
    alignment_data_path: str = "data/phase_2/outputs/alignment_data.json"
```

### 3.3 Data Contract for Phase 3 Integration
Phase 2 outputs must satisfy the following contract for Phase 3 consumption:

**Required Outputs**:
- `audio_features/`: Feature tensors with shape [T, 201] for each audio file
- `feature_metadata.json`: Complete feature extraction parameters and configurations
- `alignment_data.json`: Temporal alignment information for TJA synchronization
- `phase3_catalog.json`: Enhanced catalog with feature paths and quality metrics

**Data Quality Requirements**:
- **Feature Dimensions**: Consistent [T, 201] tensor shape across all audio files
- **Temporal Alignment**: <10ms average alignment error with TJA timing
- **Feature Quality**: >90% onset detection accuracy, <50ms timing error
- **Processing Success**: >95% of input audio files successfully processed

### 3.4 Validation Requirements
All Phase 2 outputs undergo comprehensive validation:

```python
PHASE_2_VALIDATION_REQUIREMENTS = {
    "file_existence": {
        "audio_features/": "required_directory",
        "feature_metadata.json": "required",
        "alignment_data.json": "required",
        "phase3_catalog.json": "required"
    },
    "tensor_validation": {
        "feature_shape": "[T, 201]",
        "data_type": "torch.float32",
        "value_range": "normalized"
    },
    "quality_thresholds": {
        "onset_detection_accuracy": 0.90,
        "temporal_alignment_error_ms": 10.0,
        "processing_success_rate": 0.95
    }
}
```

### 3.5 Cross-Reference
This specification aligns with:
- **Previous Phase**: [Phase 1 Data Analysis and Preprocessing RFP](Phase_1_Data_Analysis_and_Preprocessing_RFP.md) - Output Contract
- **Next Phase**: [Phase 3 TJA Sequence Processing RFP](Phase_3_TJA_Sequence_Processing_RFP.md) - Input Requirements
- **Path Management**: Standardized path resolution via `PathManager.get_phase_output_path(2)`

## 4. Small-Scale Test First

### 3.1 Test Dataset Selection
- **Sample Size**: 20 songs with diverse characteristics
- **BPM Range**: 80-200 BPM with BPM changes
- **Duration Range**: 90-300 seconds
- **Difficulty Levels**: Focus on oni/edit (levels 8-10)

### 3.2 Feature Validation Metrics
```python
def validate_extracted_features(features, audio_path, tja_metadata):
    """Comprehensive feature validation"""
    validation_results = {
        "spectral_validation": {
            "mel_spec_shape": features["mel_spectrogram"].shape,
            "frequency_range": check_frequency_coverage(features["mel_spectrogram"]),
            "dynamic_range": calculate_dynamic_range(features["mel_spectrogram"]),
            "spectral_quality": assess_spectral_quality(features["mel_spectrogram"])
        },
        "rhythmic_validation": {
            "onset_detection_rate": validate_onset_detection(features, tja_metadata),
            "tempo_accuracy": validate_tempo_estimation(features, tja_metadata),
            "beat_alignment": validate_beat_tracking(features, tja_metadata)
        },
        "temporal_validation": {
            "frame_rate_consistency": check_frame_rate(features),
            "timing_grid_accuracy": validate_timing_grid(features, tja_metadata),
            "synchronization_error": calculate_sync_error(features, tja_metadata)
        }
    }
    return validation_results
```

### 3.3 Expected Test Outputs
- **Processing Speed**: <10 seconds per 3-minute song on RTX 3070
- **Memory Usage**: <2GB GPU memory for batch processing
- **Feature Quality**: >90% onset detection accuracy, <50ms timing error
- **Synchronization**: <10ms average alignment error with TJA timing

## 5. Implementation Guidelines

### 4.1 RTX 3070 Optimized Processing (Verified Hardware)
```python
def setup_rtx3070_processing():
    """
    Configure processing specifically for RTX 3070 (8GB VRAM)
    Based on verified hardware: NVIDIA GeForce RTX 3070, CUDA 12.1, PyTorch 2.5.1+cu121
    """
    import torch

    # Verify we have the expected GPU
    if not torch.cuda.is_available():
        raise RuntimeError("CUDA not available - RTX 3070 required")

    gpu_name = torch.cuda.get_device_name(0)
    if "RTX 3070" not in gpu_name:
        logging.warning(f"Expected RTX 3070, found: {gpu_name}")

    device = torch.device("cuda:0")

    # RTX 3070 specific optimizations
    torch.backends.cudnn.benchmark = True
    torch.backends.cudnn.deterministic = False
    torch.backends.cuda.matmul.allow_tf32 = True  # Enable TF32 for faster computation
    torch.backends.cudnn.allow_tf32 = True

    # Conservative memory allocation for 8GB VRAM
    torch.cuda.set_per_process_memory_fraction(0.85)  # Use 6.8GB of 8GB
    torch.cuda.empty_cache()

    # Verify memory allocation
    total_memory = torch.cuda.get_device_properties(0).total_memory / (1024**3)
    allocated_memory = torch.cuda.memory_allocated() / (1024**3)

    logging.info(f"RTX 3070 Setup: {total_memory:.1f}GB total, {allocated_memory:.1f}GB allocated")

    return device, {
        "total_vram_gb": total_memory,
        "allocated_vram_gb": allocated_memory,
        "available_vram_gb": total_memory * 0.85,
        "recommended_batch_size": 4,
        "max_sequence_length": 2000  # frames (~40 seconds at 50fps)
    }

def batch_process_audio_features_rtx3070(audio_files, batch_size=4):
    """
    Process audio files optimized for RTX 3070 constraints
    Memory-efficient batching with real-time monitoring
    """
    device, gpu_config = setup_rtx3070_processing()

    # Adjust batch size based on available memory
    available_memory = gpu_config["available_vram_gb"]
    if available_memory < 6.0:  # Less than 6GB available
        batch_size = max(1, batch_size // 2)
        logging.warning(f"Reduced batch size to {batch_size} due to limited GPU memory")

    processed_batches = 0
    total_processing_time = 0

    for batch_start in range(0, len(audio_files), batch_size):
        batch_files = audio_files[batch_start:batch_start + batch_size]
        batch_start_time = time.time()

        # Pre-batch memory check
        memory_before = torch.cuda.memory_allocated() / (1024**3)

        try:
            # Process batch with memory monitoring
            batch_features = []
            for i, audio_file in enumerate(batch_files):
                # Check memory before each file
                current_memory = torch.cuda.memory_allocated() / (1024**3)
                if current_memory > 7.0:  # Approaching 8GB limit
                    logging.warning(f"High GPU memory usage: {current_memory:.1f}GB")
                    torch.cuda.empty_cache()

                # Extract features with error handling
                try:
                    features = extract_all_features_gpu(audio_file, device=device)
                    batch_features.append(features)
                except torch.cuda.OutOfMemoryError:
                    logging.error(f"GPU OOM processing {audio_file}")
                    torch.cuda.empty_cache()
                    # Retry on CPU as fallback
                    features = extract_all_features_cpu(audio_file)
                    batch_features.append(features)

            # Post-batch cleanup
            torch.cuda.empty_cache()
            memory_after = torch.cuda.memory_allocated() / (1024**3)

            batch_time = time.time() - batch_start_time
            total_processing_time += batch_time
            processed_batches += 1

            # Performance logging
            if processed_batches % 10 == 0:
                avg_time_per_batch = total_processing_time / processed_batches
                logging.info(f"Processed {processed_batches} batches, "
                           f"avg time: {avg_time_per_batch:.2f}s, "
                           f"memory: {memory_before:.1f}GB → {memory_after:.1f}GB")

            yield {
                "features": batch_features,
                "batch_info": {
                    "batch_index": processed_batches,
                    "processing_time": batch_time,
                    "memory_usage": {
                        "before_gb": memory_before,
                        "after_gb": memory_after,
                        "peak_gb": memory_after  # Simplified - could track actual peak
                    },
                    "files_processed": len(batch_files)
                }
            }

        except Exception as e:
            logging.error(f"Batch processing failed: {e}")
            torch.cuda.empty_cache()
            yield {
                "features": [],
                "error": str(e),
                "batch_index": processed_batches
            }

def extract_all_features_gpu(audio_file, device):
    """Extract all audio features using GPU acceleration where possible"""
    # Load audio
    audio, sr = librosa.load(audio_file, sr=44100)

    # Convert to tensor for GPU processing
    audio_tensor = torch.from_numpy(audio).float().to(device)

    # GPU-accelerated spectral features
    spectral_features = extract_spectral_features_gpu(audio_tensor, sr, device)

    # CPU-based features (librosa doesn't support GPU)
    rhythmic_features = extract_rhythmic_features_cpu(audio, sr)
    temporal_features = extract_temporal_features_cpu(audio, sr)

    # Combine features
    combined_features = torch.cat([
        spectral_features.cpu(),  # Move back to CPU for consistency
        rhythmic_features,
        temporal_features
    ], dim=-1)

    return combined_features

def extract_spectral_features_gpu(audio_tensor, sr, device):
    """Extract spectral features using GPU acceleration"""
    # Use torchaudio for GPU-accelerated spectrograms
    import torchaudio.transforms as T

    # Mel-spectrogram on GPU
    mel_transform = T.MelSpectrogram(
        sample_rate=sr,
        n_fft=2048,
        hop_length=882,  # 50fps
        n_mels=128,
        f_min=80,
        f_max=8000
    ).to(device)

    mel_spec = mel_transform(audio_tensor)
    mel_spec_db = torchaudio.functional.amplitude_to_DB(mel_spec, multiplier=10, amin=1e-10, db_multiplier=0)

    # MFCC on GPU
    mfcc_transform = T.MFCC(
        sample_rate=sr,
        n_mfcc=13,
        melkwargs={
            "n_fft": 2048,
            "hop_length": 882,
            "n_mels": 128,
            "f_min": 80,
            "f_max": 8000
        }
    ).to(device)

    mfcc = mfcc_transform(audio_tensor)

    # Chroma (using CPU fallback as torchaudio chroma is limited)
    chroma = extract_chroma_cpu(audio_tensor.cpu().numpy(), sr)
    chroma_tensor = torch.from_numpy(chroma).float().to(device)

    # Combine spectral features [T, 128+13+12=153]
    spectral_combined = torch.cat([
        mel_spec_db.transpose(0, 1),  # [T, 128]
        mfcc.transpose(0, 1),         # [T, 13]
        chroma_tensor.transpose(0, 1) # [T, 12]
    ], dim=-1)

    return spectral_combined
```

### 4.2 Memory Management Strategy
```python
def monitor_memory_usage():
    """Monitor system and GPU memory during processing"""
    import psutil
    
    # System memory
    memory = psutil.virtual_memory()
    
    # GPU memory
    gpu_memory = {
        "allocated": 0,
        "cached": 0,
        "total": 0
    }
    
    if torch.cuda.is_available():
        gpu_memory["allocated"] = torch.cuda.memory_allocated() / (1024**3)
        gpu_memory["cached"] = torch.cuda.memory_reserved() / (1024**3)
        gpu_memory["total"] = torch.cuda.get_device_properties(0).total_memory / (1024**3)
    
    return {
        "system_ram_gb": memory.used / (1024**3),
        "system_ram_percent": memory.percent,
        "gpu_memory_gb": gpu_memory
    }
```

## 6. Best Practices

### 5.1 Feature Standardization
- **Normalization**: Z-score normalization per feature channel
- **Temporal Consistency**: Fixed frame rate across all songs
- **Feature Scaling**: Robust scaling to handle dynamic range variations
- **Missing Data**: Interpolation strategies for corrupted audio segments

### 5.2 Quality Assurance
- **Feature Visualization**: Generate spectrograms for manual inspection
- **Statistical Validation**: Feature distribution analysis
- **Correlation Analysis**: Cross-feature correlation matrices
- **Outlier Detection**: Identify and flag anomalous feature patterns

### 5.3 Reproducibility
```python
def set_audio_processing_seeds():
    """Set seeds for reproducible audio processing"""
    np.random.seed(42)
    torch.manual_seed(42)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(42)
        torch.cuda.manual_seed_all(42)
```

## 7. Challenges and Edge Cases

### 6.1 Audio Quality Issues
- **Low SNR Audio**: Noise reduction preprocessing
- **Clipping/Distortion**: Dynamic range compression detection
- **Silence Regions**: Padding vs. actual silence differentiation
- **Format Artifacts**: OGG compression artifact handling

### 6.2 Temporal Alignment Challenges
- **BPM Changes**: Smooth interpolation between tempo changes
- **Offset Precision**: Sub-frame offset alignment accuracy
- **Timing Drift**: Cumulative timing error correction
- **Complex Time Signatures**: Non-4/4 time signature handling

### 6.3 Feature Extraction Edge Cases
- **Extreme Tempos**: Very slow (<80 BPM) or fast (>200 BPM) songs
- **Sparse Instrumentation**: Minimal percussion content
- **Dense Arrangements**: Overlapping frequency content
- **Electronic vs. Acoustic**: Different onset characteristics

### 6.4 Mitigation Strategies
```python
def handle_bpm_changes(audio, bpm_changes, offset):
    """Robust handling of BPM changes in audio processing"""
    # Create time-varying window sizes for STFT
    adaptive_hop_lengths = []
    for time, bpm in bpm_changes:
        # Adjust hop length based on current BPM
        hop_length = int(44100 / (bpm / 60 * 4))  # 4 analysis points per beat
        adaptive_hop_lengths.append((time, hop_length))
    
    return adaptive_hop_lengths

def validate_feature_quality(features, thresholds):
    """Validate extracted features against quality thresholds"""
    quality_checks = {
        "spectral_coverage": np.mean(features["mel_spectrogram"]) > thresholds["min_spectral_energy"],
        "onset_density": len(features["onset_positions"]) > thresholds["min_onsets"],
        "temporal_consistency": check_temporal_consistency(features),
        "synchronization_error": calculate_sync_error(features) < thresholds["max_sync_error"]
    }
    
    return all(quality_checks.values()), quality_checks
```

## 8. Dependencies

### 7.1 Required Inputs from Phase 1
- Validated data catalog with quality metrics
- Cleaned audio files in standardized format
- TJA metadata including BPM sequences and timing commands
- Processing statistics and recommendations

### 7.2 External Libraries
```python
# Core audio processing
import librosa
import torchaudio
import torch
import numpy as np

# Specialized audio analysis
import essentia
import madmom  # For advanced onset detection
import pyrubberband  # For time-stretching if needed

# Visualization and validation
import matplotlib.pyplot as plt
import seaborn as sns
```

## 9. System Resource Monitoring

### 8.1 GPU Memory Management
```python
def optimize_gpu_memory():
    """Optimize GPU memory usage for RTX 3070"""
    if torch.cuda.is_available():
        # Clear cache
        torch.cuda.empty_cache()
        
        # Set memory growth
        torch.cuda.set_per_process_memory_fraction(0.8)
        
        # Monitor memory usage
        allocated = torch.cuda.memory_allocated() / (1024**3)
        reserved = torch.cuda.memory_reserved() / (1024**3)
        
        if allocated > 6.0:  # Warning threshold for 8GB GPU
            logging.warning(f"High GPU memory usage: {allocated:.2f}GB allocated")
            torch.cuda.empty_cache()
        
        return allocated, reserved
    
    return 0, 0
```

### 8.2 Processing Performance Monitoring
```python
def benchmark_feature_extraction():
    """Benchmark feature extraction performance"""
    import time
    
    start_time = time.time()
    memory_start = monitor_memory_usage()
    
    # Process sample audio
    sample_audio = torch.randn(44100 * 180)  # 3-minute audio
    features = extract_all_features(sample_audio)
    
    end_time = time.time()
    memory_end = monitor_memory_usage()
    
    return {
        "processing_time": end_time - start_time,
        "memory_delta": memory_end["system_ram_gb"] - memory_start["system_ram_gb"],
        "gpu_memory_peak": memory_end["gpu_memory_gb"]["allocated"],
        "features_per_second": len(features["mel_spectrogram"]) / (end_time - start_time)
    }
```

## 10. Advanced Feature Engineering

### 9.1 Multi-Resolution Analysis
```python
def extract_multi_resolution_features(audio, sr=44100):
    """Extract features at multiple temporal resolutions"""
    resolutions = [
        {"frame_rate": 100, "context": "fine_grain"},    # 10ms frames for precise timing
        {"frame_rate": 50, "context": "standard"},       # 20ms frames for general analysis
        {"frame_rate": 25, "context": "coarse_grain"}    # 40ms frames for global patterns
    ]

    multi_res_features = {}
    for res in resolutions:
        hop_length = sr // res["frame_rate"]
        features = extract_spectral_features(audio, sr, res["frame_rate"])
        multi_res_features[res["context"]] = features

    return multi_res_features
```

### 9.2 Rhythm-Specific Feature Engineering
```python
def extract_drum_specific_features(audio, sr=44100):
    """Extract features optimized for drum/percussion detection"""
    # Frequency bands for different drum components
    drum_bands = {
        "kick": (20, 120),      # Bass drum fundamental
        "snare": (150, 300),    # Snare drum body
        "hihat": (8000, 16000), # Hi-hat frequencies
        "crash": (3000, 8000)   # Cymbal frequencies
    }

    drum_features = {}
    for drum_type, (fmin, fmax) in drum_bands.items():
        # Band-limited spectral features
        filtered_audio = librosa.effects.preemphasis(audio)
        band_energy = librosa.feature.spectral_centroid(
            y=filtered_audio, sr=sr, fmin=fmin, fmax=fmax
        )
        drum_features[f"{drum_type}_energy"] = band_energy

    return drum_features
```

### 9.3 Temporal Context Windows
```python
def create_temporal_context(features, window_size=5):
    """Create temporal context windows for sequence modeling"""
    # Add past and future context to each frame
    padded_features = torch.nn.functional.pad(
        features, (0, 0, window_size//2, window_size//2), mode='reflect'
    )

    # Create sliding windows
    context_features = []
    for i in range(features.shape[0]):
        window = padded_features[i:i+window_size]
        context_features.append(window.flatten())

    return torch.stack(context_features)
```

## 11. Quality Assurance Framework

### 10.1 Automated Feature Validation
```python
def comprehensive_feature_validation(song_id, features, metadata):
    """Comprehensive automated validation of extracted features"""
    validation_report = {
        "song_id": song_id,
        "timestamp": datetime.now().isoformat(),
        "validations": {}
    }

    # Spectral validation
    mel_spec = features["mel_spectrogram"]
    validation_report["validations"]["spectral"] = {
        "shape_valid": mel_spec.shape[1] == 128,
        "no_nan_values": not torch.isnan(mel_spec).any(),
        "dynamic_range": float(mel_spec.max() - mel_spec.min()),
        "energy_distribution": validate_energy_distribution(mel_spec)
    }

    # Rhythmic validation
    onset_strength = features["onset_strength"]
    validation_report["validations"]["rhythmic"] = {
        "onset_density": float(torch.mean(onset_strength > 0.1)),
        "peak_prominence": validate_onset_peaks(onset_strength),
        "tempo_consistency": validate_tempo_stability(features["tempo"])
    }

    # Temporal alignment validation
    timing_grid = features["timing_grid"]
    validation_report["validations"]["temporal"] = {
        "grid_alignment": validate_beat_grid_alignment(timing_grid, metadata),
        "bpm_tracking": validate_bpm_sequence(features["bpm_sequence"], metadata),
        "offset_accuracy": validate_offset_alignment(features, metadata)
    }

    # Overall quality score
    validation_report["quality_score"] = calculate_overall_quality_score(
        validation_report["validations"]
    )

    return validation_report
```

### 10.2 Statistical Quality Metrics
```python
def calculate_feature_statistics(all_features):
    """Calculate dataset-wide feature statistics for quality assessment"""
    stats = {
        "spectral_stats": {
            "mel_spec_mean": torch.mean(torch.stack([f["mel_spectrogram"] for f in all_features])),
            "mel_spec_std": torch.std(torch.stack([f["mel_spectrogram"] for f in all_features])),
            "frequency_coverage": analyze_frequency_coverage(all_features)
        },
        "rhythmic_stats": {
            "onset_density_distribution": analyze_onset_density_distribution(all_features),
            "tempo_range": analyze_tempo_range(all_features),
            "beat_tracking_accuracy": analyze_beat_tracking_accuracy(all_features)
        },
        "temporal_stats": {
            "synchronization_errors": analyze_synchronization_errors(all_features),
            "timing_grid_quality": analyze_timing_grid_quality(all_features),
            "bpm_change_handling": analyze_bpm_change_handling(all_features)
        }
    }

    return stats
```

## 12. Performance Optimization

### 11.1 Caching Strategy
```python
def setup_feature_caching():
    """Setup intelligent caching for processed features"""
    cache_config = {
        "cache_dir": "data/processed/cache/",
        "max_cache_size_gb": 20,
        "cache_compression": True,
        "cache_validation": True
    }

    def cache_features(song_id, features):
        cache_path = Path(cache_config["cache_dir"]) / f"{song_id}_features.pt"
        torch.save(features, cache_path, _use_new_zipfile_serialization=True)

        # Compress if enabled
        if cache_config["cache_compression"]:
            compress_cache_file(cache_path)

    def load_cached_features(song_id):
        cache_path = Path(cache_config["cache_dir"]) / f"{song_id}_features.pt"
        if cache_path.exists():
            return torch.load(cache_path, map_location='cpu')
        return None

    return cache_features, load_cached_features
```

### 11.2 Parallel Processing Pipeline
```python
def parallel_feature_extraction(song_list, num_workers=4):
    """Parallel feature extraction with resource management"""
    from concurrent.futures import ProcessPoolExecutor, as_completed
    import multiprocessing as mp

    # Limit workers based on available resources
    max_workers = min(num_workers, mp.cpu_count() - 1)

    def process_single_song(song_info):
        """Process a single song with error handling"""
        try:
            song_id = song_info["song_id"]
            audio_path = song_info["audio_path"]
            tja_metadata = song_info["metadata"]

            # Load and process audio
            audio, sr = librosa.load(audio_path, sr=44100)
            features = extract_all_features(audio, sr, tja_metadata)

            # Validate features
            validation = comprehensive_feature_validation(song_id, features, tja_metadata)

            return {
                "song_id": song_id,
                "features": features,
                "validation": validation,
                "status": "success"
            }

        except Exception as e:
            return {
                "song_id": song_info.get("song_id", "unknown"),
                "error": str(e),
                "status": "failed"
            }

    # Process songs in parallel
    results = []
    with ProcessPoolExecutor(max_workers=max_workers) as executor:
        future_to_song = {
            executor.submit(process_single_song, song): song
            for song in song_list
        }

        for future in as_completed(future_to_song):
            result = future.result()
            results.append(result)

            # Log progress
            if len(results) % 10 == 0:
                success_rate = sum(1 for r in results if r["status"] == "success") / len(results)
                logging.info(f"Processed {len(results)} songs, success rate: {success_rate:.2%}")

    return results
```

## 13. Deliverables and Integration

### 12.1 Feature Dataset Format
```python
# Standardized feature dataset structure
feature_dataset = {
    "metadata": {
        "version": "1.0",
        "creation_date": "2024-07-24",
        "total_songs": 2000,
        "feature_dimensions": {
            "spectral": 128,
            "rhythmic": 64,
            "harmonic": 32,
            "temporal": 16
        },
        "frame_rate": 50.0,
        "sample_rate": 44100
    },
    "songs": {
        "song_id_001": {
            "features": torch.tensor([T, 240]),  # Combined feature tensor
            "timing_grid": torch.tensor([T, 4]),
            "metadata": {...},
            "validation": {...}
        }
    },
    "statistics": {
        "feature_means": torch.tensor([240]),
        "feature_stds": torch.tensor([240]),
        "quality_distribution": {...}
    }
}
```

### 12.2 Integration with Phase 3
```python
def prepare_features_for_sequence_modeling():
    """Prepare extracted features for TJA sequence modeling"""
    # Standardize feature dimensions
    # Create temporal alignment indices
    # Generate feature-to-note correspondence maps
    # Export in format compatible with sequence models
    pass
```

---

## 14. Success Criteria and Performance Targets

### 8.1 Hardware Resource Efficiency Requirements (Mandatory)
```python
PHASE_2_RESOURCE_EFFICIENCY_TARGETS = {
    "gpu_resource_utilization": {
        "sustained_gpu_utilization": 0.80,        # 80% sustained GPU utilization
        "vram_efficiency": 0.90,                  # 90% VRAM efficiency
        "compute_unit_occupancy": 0.85,           # 85% compute unit occupancy
        "gpu_memory_bandwidth_utilization": 0.88, # 88% GPU memory bandwidth usage
        "tensor_core_utilization": 0.75,         # 75% tensor core utilization
        "max_gpu_memory_gb": 6.0                 # Never exceed 6GB GPU memory
    },
    "cpu_memory_efficiency": {
        "memory_utilization": 0.85,               # 85% memory efficiency
        "max_ram_usage_gb": 20,                   # Never exceed 20GB RAM
        "cache_hit_ratio": 0.92,                  # 92% cache hit ratio
        "memory_bandwidth_utilization": 0.82,     # 82% memory bandwidth usage
        "cache_utilization_gb": 16,               # Use 16GB for caching
        "buffer_efficiency": 0.88                 # 88% buffer efficiency
    },
    "parallel_processing_efficiency": {
        "worker_utilization": 0.85,               # 85% worker utilization
        "parallel_processing_workers": 4,         # 4 parallel audio processors
        "load_balancing_efficiency": 0.90,        # 90% load balancing efficiency
        "synchronization_overhead": 0.05          # <5% synchronization overhead
    },
    "feature_quality": {
        "temporal_alignment_accuracy": 0.95,      # 95% temporal alignment
        "feature_extraction_success": 0.98,       # 98% extraction success
        "synchronization_accuracy": 0.97,         # 97% sync accuracy
        "feature_consistency": 0.94               # 94% feature consistency
    }
}
```

### 8.2 Phase 3 Handoff Requirements
**Output Validation for Phase 3 Consumption:**
- Audio features tensor `[T, 201]` with validated dimensions
- Temporal alignment grids synchronized with TJA timing
- Feature quality metrics above threshold
- Hardware performance documentation

### 8.3 Phase 2 Completion Criteria
**Data Processing Requirements:**
- [ ] Multi-scale audio features extracted for all validated songs
- [ ] Precise temporal alignment with TJA timing grids
- [ ] GPU-optimized processing pipeline with memory management
- [ ] Comprehensive feature validation and quality metrics
- [ ] Synchronized feature tensors ready for neural network training

**Hardware Optimization Requirements:**
- [ ] GPU utilization target: 80% (RTX 3070 optimization)
- [ ] Memory utilization: <20GB RAM, 16GB cache
- [ ] Processing speed: 20 songs per minute minimum
- [ ] Real-time performance monitoring

**Estimated Timeline:** 2-3 weeks
**Resource Requirements:** GPU-intensive, hardware-optimized, temporal processing
**Critical Dependencies:** Phase 1 validated catalog, RTX 3070 optimization implementation

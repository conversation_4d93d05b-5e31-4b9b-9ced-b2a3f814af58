# Phase 5: Model Training and Optimization - Request for Proposal (RFP)
## Self-Contained Implementation Specification

## 1. Phase Overview and System Context

### 1.1 Project Objective
Implement a **TJA rhythm chart generation system** that creates high-quality Taiko no Tatsufin charts from audio input using deep learning. This system analyzes audio files and generates appropriate note sequences for difficulty levels 8-10 (Oni/Edit courses).

### 1.2 Phase 5 Purpose and Role
Phase 5 implements the complete training pipeline for the TJA generation model, including hyperparameter optimization, training strategies, validation protocols, and model optimization techniques. This phase transforms the neural architecture from Phase 4 into a production-ready model capable of generating high-quality, difficulty-appropriate TJA charts from audio input.

### 1.3 Implementation Status and Performance
**Status**: 📋 **PLANNED** - Training pipeline components organized

**Target Performance Metrics**:
- **Training Time**: <48 hours for full training
- **Model Quality**: >85% validation accuracy
- **Hardware Efficiency**: 85% GPU utilization
- **Memory Usage**: <6.8GB VRAM sustained

**Key Implementation Files**:
- `src/phase_5/controller.py` - Main phase controller
- `src/phase_5/advanced_trainer.py` - Advanced training strategies
- `src/phase_5/hyperparameter_optimizer.py` - Hyperparameter optimization
- `src/phase_5/training_strategies.py` - Curriculum learning
- `src/phase_5/data_augmentation.py` - Data augmentation techniques
- `src/phase_5/model_ensemble.py` - Ensemble training methods

### 1.4 Hardware Environment (Verified)
**CRITICAL**: All implementations must be optimized for the verified hardware environment:

```python
HARDWARE_SPECS = {
    "gpu": {
        "model": "NVIDIA GeForce RTX 3070",
        "vram_gb": 8.0,
        "cuda_version": "12.1",
        "pytorch_version": "2.5.1+cu121",
        "memory_constraint": "6.8GB usable (safety margin)",
        "optimization_target": "model_training"
    },
    "cpu": {
        "physical_cores": 8,
        "logical_cores": 16,
        "recommended_workers": 6,  # Reduced for intensive GPU training
        "optimization_target": "data_loading_training"
    },
    "memory": {
        "total_ram_gb": 31.8,
        "available_ram_gb": 28.0,
        "recommended_cache_gb": 10,  # Reduced for training
        "training_memory_gb": 12     # Reserved for training process
    }
}
```

### 1.5 Phase Dependencies and Data Flow
- **Previous Phase**: Phase 4 (Neural Network Architecture) - requires complete model architecture
- **Next Phase**: Phase 6 (Inference Pipeline and Validation)
- **Input Contract**: Model architecture and training datasets from Phases 1-4
- **Output Contract**: Trained model weights optimized for RTX 3070 inference
- **Data Flow**: Model architecture + datasets → Training optimization → Trained weights → Phase 6 input

## 2. Detailed Specification

### 2.1 Input Requirements
- **Model Architecture**: Complete TJAGenerationModel from Phase 4
- **Training Dataset**: Aligned audio-sequence pairs from Phase 3 (70% split)
- **Validation Dataset**: Validation split (15%) with ground truth charts
- **Test Dataset**: Hold-out test set (15%) for final evaluation
- **Hardware**: RTX 3070 (8GB VRAM), 32GB RAM, multi-core CPU

### 2.2 Training Pipeline Specification

#### 2.2.1 Training Configuration
```python
@dataclass
class TrainingConfig:
    """Comprehensive training configuration"""
    
    # Model parameters
    model_name: str = "tja_generation_v1"
    checkpoint_dir: str = "checkpoints/"
    
    # Training hyperparameters
    batch_size: int = 2  # Optimized for RTX 3070
    gradient_accumulation_steps: int = 8  # Effective batch size = 16
    learning_rate: float = 1e-4
    weight_decay: float = 0.01
    max_grad_norm: float = 1.0
    
    # Learning rate scheduling
    warmup_steps: int = 1000
    total_training_steps: int = 50000
    lr_scheduler: str = "cosine_with_warmup"
    
    # Loss function weights
    loss_weights: Dict[str, float] = field(default_factory=lambda: {
        'note_type': 1.0,
        'density': 0.5,
        'pattern': 0.3,
        'temporal': 0.2,
        'difficulty': 0.1
    })
    
    # Optimization settings
    mixed_precision: bool = True
    gradient_checkpointing: bool = True
    compile_model: bool = True  # PyTorch 2.0 compilation
    
    # Validation and checkpointing
    validation_frequency: int = 500  # Steps
    checkpoint_frequency: int = 2000  # Steps
    early_stopping_patience: int = 10  # Validation cycles
    
    # Data augmentation
    use_data_augmentation: bool = True
    augmentation_probability: float = 0.3
    
    # Regularization
    dropout_rate: float = 0.1
    label_smoothing: float = 0.1
    
    # Hardware optimization
    num_workers: int = 4
    pin_memory: bool = True
    persistent_workers: bool = True
```

#### 2.2.2 Advanced Training Strategy
```python
class AdvancedTrainingStrategy:
    """Advanced training strategy with curriculum learning and adaptive techniques"""
    
    def __init__(self, config: TrainingConfig):
        self.config = config
        self.current_step = 0
        self.best_validation_loss = float('inf')
        self.patience_counter = 0
        
        # Curriculum learning stages
        self.curriculum_stages = [
            {"name": "basic_patterns", "steps": 10000, "difficulty_focus": [8]},
            {"name": "intermediate_patterns", "steps": 20000, "difficulty_focus": [8, 9]},
            {"name": "advanced_patterns", "steps": 20000, "difficulty_focus": [8, 9, 10]}
        ]
        
        # Adaptive training parameters
        self.adaptive_params = {
            "loss_weight_adaptation": True,
            "learning_rate_adaptation": True,
            "batch_size_adaptation": False  # Fixed for memory constraints
        }
    
    def get_current_curriculum_stage(self):
        """Get current curriculum learning stage"""
        cumulative_steps = 0
        for stage in self.curriculum_stages:
            cumulative_steps += stage["steps"]
            if self.current_step < cumulative_steps:
                return stage
        return self.curriculum_stages[-1]  # Final stage
    
    def adapt_loss_weights(self, current_losses, validation_metrics):
        """Dynamically adapt loss weights based on training progress"""
        if not self.adaptive_params["loss_weight_adaptation"]:
            return self.config.loss_weights
        
        adapted_weights = self.config.loss_weights.copy()
        
        # Increase weight for components with high loss
        for loss_name, loss_value in current_losses.items():
            if loss_name in adapted_weights:
                if loss_value > 1.0:  # High loss threshold
                    adapted_weights[loss_name] *= 1.1
                elif loss_value < 0.1:  # Low loss threshold
                    adapted_weights[loss_name] *= 0.9
        
        # Normalize weights
        total_weight = sum(adapted_weights.values())
        adapted_weights = {k: v / total_weight for k, v in adapted_weights.items()}
        
        return adapted_weights
    
    def should_continue_training(self, validation_loss):
        """Determine if training should continue based on early stopping criteria"""
        if validation_loss < self.best_validation_loss:
            self.best_validation_loss = validation_loss
            self.patience_counter = 0
            return True, "improvement"
        else:
            self.patience_counter += 1
            if self.patience_counter >= self.config.early_stopping_patience:
                return False, "early_stopping"
            return True, "no_improvement"
```

#### 2.2.3 Data Loading and Augmentation
```python
class TJADataLoader:
    """Optimized data loader with advanced augmentation"""
    
    def __init__(self, dataset, config: TrainingConfig, is_training=True):
        self.dataset = dataset
        self.config = config
        self.is_training = is_training
        
        # Initialize augmentation strategies
        if is_training and config.use_data_augmentation:
            self.augmentation = TJAAugmentation(config.augmentation_probability)
        else:
            self.augmentation = None
    
    def create_dataloader(self):
        """Create optimized PyTorch DataLoader"""
        return DataLoader(
            self.dataset,
            batch_size=self.config.batch_size,
            shuffle=self.is_training,
            num_workers=self.config.num_workers,
            pin_memory=self.config.pin_memory,
            persistent_workers=self.config.persistent_workers,
            collate_fn=self.collate_fn,
            drop_last=self.is_training
        )
    
    def collate_fn(self, batch):
        """Custom collate function with padding and augmentation"""
        # Extract batch components
        audio_features = [item['audio_features'] for item in batch]
        note_sequences = [item['note_sequences'] for item in batch]
        metadata = [item['metadata'] for item in batch]
        
        # Pad sequences to same length
        max_length = max(len(seq) for seq in audio_features)
        
        padded_audio = torch.zeros(len(batch), max_length, audio_features[0].shape[-1])
        padded_notes = torch.zeros(len(batch), max_length, note_sequences[0].shape[-1])
        attention_masks = torch.zeros(len(batch), max_length, dtype=torch.bool)
        
        for i, (audio, notes) in enumerate(zip(audio_features, note_sequences)):
            seq_len = len(audio)
            padded_audio[i, :seq_len] = audio
            padded_notes[i, :seq_len] = notes
            attention_masks[i, seq_len:] = True  # Mask padded positions
        
        # Apply augmentation if training
        if self.augmentation is not None:
            padded_audio, padded_notes = self.augmentation(padded_audio, padded_notes, metadata)
        
        return {
            'audio_features': padded_audio,
            'note_sequences': padded_notes,
            'attention_masks': attention_masks,
            'metadata': metadata
        }

class TJAAugmentation:
    """Advanced data augmentation for TJA training"""
    
    def __init__(self, augmentation_probability=0.3):
        self.prob = augmentation_probability
        
        # Augmentation strategies
        self.strategies = [
            self.tempo_augmentation,
            self.pitch_shift_augmentation,
            self.noise_injection,
            self.time_masking,
            self.frequency_masking
        ]
    
    def __call__(self, audio_features, note_sequences, metadata):
        """Apply random augmentation strategies"""
        if torch.rand(1).item() > self.prob:
            return audio_features, note_sequences
        
        # Randomly select and apply augmentation
        strategy = torch.randint(0, len(self.strategies), (1,)).item()
        return self.strategies[strategy](audio_features, note_sequences, metadata)
    
    def tempo_augmentation(self, audio_features, note_sequences, metadata):
        """Apply tempo scaling augmentation"""
        # Tempo scaling factor (0.9 to 1.1)
        tempo_factor = 0.9 + torch.rand(1).item() * 0.2
        
        # Apply time stretching to audio features
        stretched_audio = self.apply_time_stretch(audio_features, tempo_factor)
        
        # Adjust note sequence timing accordingly
        adjusted_notes = self.adjust_note_timing(note_sequences, tempo_factor)
        
        return stretched_audio, adjusted_notes
    
    def pitch_shift_augmentation(self, audio_features, note_sequences, metadata):
        """Apply pitch shifting to audio features"""
        # Pitch shift in semitones (-2 to +2)
        pitch_shift = (torch.rand(1).item() - 0.5) * 4
        
        # Apply pitch shift to spectral features
        shifted_audio = self.apply_pitch_shift(audio_features, pitch_shift)
        
        # Note sequences remain unchanged for pitch shifts
        return shifted_audio, note_sequences
```

### 2.3 Training Monitoring and Metrics

#### 2.3.1 Comprehensive Metrics Tracking
```python
class TrainingMetrics:
    """Comprehensive metrics tracking for TJA model training"""
    
    def __init__(self):
        self.metrics_history = {
            'train_losses': defaultdict(list),
            'val_losses': defaultdict(list),
            'learning_rates': [],
            'gradient_norms': [],
            'model_metrics': defaultdict(list),
            'hardware_metrics': defaultdict(list)
        }
        
        # Specialized TJA metrics
        self.tja_metrics = TJASpecificMetrics()
    
    def update_training_metrics(self, step, losses, learning_rate, grad_norm):
        """Update training metrics"""
        for loss_name, loss_value in losses.items():
            self.metrics_history['train_losses'][loss_name].append({
                'step': step,
                'value': loss_value.item() if torch.is_tensor(loss_value) else loss_value
            })
        
        self.metrics_history['learning_rates'].append({
            'step': step,
            'value': learning_rate
        })
        
        self.metrics_history['gradient_norms'].append({
            'step': step,
            'value': grad_norm
        })
    
    def update_validation_metrics(self, step, val_losses, model_outputs, targets):
        """Update validation metrics with TJA-specific evaluations"""
        # Standard validation losses
        for loss_name, loss_value in val_losses.items():
            self.metrics_history['val_losses'][loss_name].append({
                'step': step,
                'value': loss_value.item() if torch.is_tensor(loss_value) else loss_value
            })
        
        # TJA-specific metrics
        tja_metrics = self.tja_metrics.calculate_metrics(model_outputs, targets)
        for metric_name, metric_value in tja_metrics.items():
            self.metrics_history['model_metrics'][metric_name].append({
                'step': step,
                'value': metric_value
            })
    
    def update_hardware_metrics(self, step):
        """Update hardware utilization metrics"""
        hardware_stats = self.get_hardware_stats()
        for stat_name, stat_value in hardware_stats.items():
            self.metrics_history['hardware_metrics'][stat_name].append({
                'step': step,
                'value': stat_value
            })
    
    def get_hardware_stats(self):
        """Get current hardware utilization statistics"""
        stats = {}
        
        # GPU metrics
        if torch.cuda.is_available():
            stats['gpu_memory_allocated'] = torch.cuda.memory_allocated() / (1024**3)
            stats['gpu_memory_reserved'] = torch.cuda.memory_reserved() / (1024**3)
            stats['gpu_utilization'] = torch.cuda.utilization() if hasattr(torch.cuda, 'utilization') else 0
        
        # CPU and RAM metrics
        import psutil
        stats['cpu_percent'] = psutil.cpu_percent()
        stats['ram_percent'] = psutil.virtual_memory().percent
        stats['ram_used_gb'] = psutil.virtual_memory().used / (1024**3)
        
        return stats

class TJASpecificMetrics:
    """TJA-specific evaluation metrics"""
    
    def __init__(self):
        self.note_type_accuracy = torchmetrics.Accuracy(task='multiclass', num_classes=8)
        self.pattern_similarity = PatternSimilarityMetric()
        self.difficulty_consistency = DifficultyConsistencyMetric()
        self.temporal_alignment = TemporalAlignmentMetric()
    
    def calculate_metrics(self, model_outputs, targets):
        """Calculate comprehensive TJA-specific metrics"""
        metrics = {}
        
        # Note type accuracy
        note_pred = model_outputs['note_types'].argmax(dim=-1)
        note_target = targets['note_types'].argmax(dim=-1)
        metrics['note_type_accuracy'] = self.note_type_accuracy(note_pred, note_target).item()
        
        # Pattern similarity
        pattern_pred = model_outputs['pattern_context']
        pattern_target = targets['pattern_context']
        metrics['pattern_similarity'] = self.pattern_similarity(pattern_pred, pattern_target).item()
        
        # Difficulty consistency
        metrics['difficulty_consistency'] = self.difficulty_consistency(model_outputs, targets).item()
        
        # Temporal alignment accuracy
        metrics['temporal_alignment'] = self.temporal_alignment(model_outputs, targets).item()
        
        # Note density correlation
        density_pred = model_outputs['note_density']
        density_target = targets['note_density']
        metrics['density_correlation'] = torch.corrcoef(torch.stack([
            density_pred.flatten(), density_target.flatten()
        ]))[0, 1].item()
        
        return metrics
```

## 3. Output Path Specifications

### 3.1 Standardized Directory Structure
Phase 5 outputs are organized within the standardized `data/phase_5/` directory structure:

```
data/phase_5/
├── outputs/                   # Primary phase outputs
│   ├── trained_models/       # Final trained model files
│   │   ├── final_model.pt        # Best trained model checkpoint
│   │   ├── model_state_dict.pt   # Model state dictionary
│   │   ├── optimizer_state.pt    # Optimizer state
│   │   └── training_config.json  # Training configuration used
│   ├── checkpoints/          # Training checkpoints
│   │   ├── epoch_001.pt          # Periodic training checkpoints
│   │   ├── epoch_010.pt
│   │   └── best_validation.pt    # Best validation checkpoint
│   ├── training_logs/        # Comprehensive training logs
│   │   ├── training_metrics.json
│   │   ├── validation_metrics.json
│   │   └── loss_curves.json
│   └── optimization_results.json # Training optimization results
├── metadata/                 # Processing metadata
│   ├── training_statistics.json
│   ├── hyperparameter_search.json
│   └── model_performance.json
├── validation/               # Validation results
│   ├── training_validation_report.json
│   ├── model_quality_assessment.json
│   └── convergence_analysis.json
├── logs/                     # Phase-specific logs
│   ├── phase5_training.log
│   ├── optimization.log
│   └── gpu_utilization.log
└── temp/                     # Temporary processing files
    ├── intermediate_checkpoints/
    └── training_cache/
```

### 3.2 Output Schema Specification
Phase 5 implements the `Phase5Output` schema extending the base `StandardizedOutput`:

```python
@dataclass
class Phase5Output(StandardizedOutput):
    phase_number: int = 5
    phase_name: str = "Model Training Optimization"

    # Phase-specific outputs
    final_model_path: str            # Path to final trained model
    training_epochs: int             # Number of training epochs completed
    final_validation_accuracy: float # Final validation accuracy achieved
    training_time_hours: float       # Total training time in hours
    best_checkpoint_path: str        # Path to best checkpoint

    # File paths
    trained_models_dir: str = "data/phase_5/outputs/trained_models/"
    checkpoints_dir: str = "data/phase_5/outputs/checkpoints/"
    training_logs_dir: str = "data/phase_5/outputs/training_logs/"
```

### 3.3 Data Contract for Phase 6 Integration
Phase 5 outputs must satisfy the following contract for Phase 6 consumption:

**Required Outputs**:
- `trained_models/final_model.pt`: Best trained model ready for inference
- `training_logs/`: Complete training metrics and performance data
- `optimization_results.json`: Training optimization results and hyperparameters
- `checkpoints/best_validation.pt`: Best validation checkpoint for fallback

**Data Quality Requirements**:
- **Model Quality**: >85% validation accuracy on test dataset
- **Training Convergence**: Stable convergence with <5% validation loss variance
- **Hardware Efficiency**: 85% GPU utilization during training
- **Model Compatibility**: Compatible with inference pipeline requirements

### 3.4 Validation Requirements
All Phase 5 outputs undergo comprehensive validation:

```python
PHASE_5_VALIDATION_REQUIREMENTS = {
    "file_existence": {
        "trained_models/final_model.pt": "required",
        "training_logs/": "required_directory",
        "optimization_results.json": "required",
        "checkpoints/best_validation.pt": "required"
    },
    "model_validation": {
        "model_loadable": True,
        "inference_compatible": True,
        "memory_requirements": "<6.8GB"
    },
    "quality_thresholds": {
        "final_validation_accuracy": 0.85,
        "training_convergence": True,
        "gpu_utilization_efficiency": 0.85
    }
}
```

### 3.5 Cross-Reference
This specification aligns with:
- **Previous Phase**: [Phase 4 Neural Network Architecture RFP](Phase_4_Neural_Network_Architecture_RFP.md) - Output Contract
- **Next Phase**: [Phase 6 Inference Pipeline Validation RFP](Phase_6_Inference_Pipeline_Validation_RFP.md) - Input Requirements
- **Path Management**: Standardized path resolution via `PathManager.get_phase_output_path(5)`

## 4. Small-Scale Test First

### 4.1 Training Pipeline Validation
- **Mini-Dataset**: 100 songs for initial training validation
- **Reduced Epochs**: 5 epochs to verify training stability
- **Memory Profiling**: Continuous monitoring of GPU/RAM usage
- **Gradient Validation**: Check for gradient explosion/vanishing

### 3.2 Hyperparameter Sensitivity Analysis
```python
def hyperparameter_sensitivity_test():
    """Test sensitivity to key hyperparameters"""
    test_configs = [
        {"learning_rate": 1e-5, "batch_size": 1, "name": "conservative"},
        {"learning_rate": 1e-4, "batch_size": 2, "name": "baseline"},
        {"learning_rate": 5e-4, "batch_size": 4, "name": "aggressive"},
    ]
    
    results = {}
    for config in test_configs:
        # Train for 1000 steps with each configuration
        trainer = TJATrainer(config)
        metrics = trainer.train_steps(1000)
        results[config["name"]] = {
            "final_loss": metrics["train_losses"]["total"][-1],
            "convergence_rate": calculate_convergence_rate(metrics["train_losses"]["total"]),
            "memory_usage": metrics["hardware_metrics"]["gpu_memory_allocated"][-1],
            "training_stability": calculate_stability_score(metrics["train_losses"]["total"])
        }
    
    return results
```

### 3.3 Expected Test Results
- **Training Stability**: Loss should decrease consistently without oscillation
- **Memory Usage**: <7GB GPU memory with batch_size=2
- **Convergence Rate**: 10-20% loss reduction in first 1000 steps
- **Gradient Health**: Gradient norms between 0.1-10.0

## 4. Implementation Guidelines

### 4.1 Training Loop Implementation
```python
class TJATrainer:
    """Main training class for TJA generation model"""
    
    def __init__(self, config: TrainingConfig):
        self.config = config
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
        # Initialize model and move to device
        self.model = TJAGenerationModel(config).to(self.device)
        
        # Compile model for PyTorch 2.0 optimization
        if config.compile_model and hasattr(torch, 'compile'):
            self.model = torch.compile(self.model)
        
        # Initialize training components
        self.optimizer = self.create_optimizer()
        self.scheduler = self.create_scheduler()
        self.loss_function = TJAGenerationLoss(config.loss_weights)
        self.scaler = torch.cuda.amp.GradScaler() if config.mixed_precision else None
        
        # Initialize tracking
        self.metrics = TrainingMetrics()
        self.strategy = AdvancedTrainingStrategy(config)
        
        # Initialize data loaders
        self.train_loader = None
        self.val_loader = None
    
    def train(self, train_dataset, val_dataset):
        """Main training loop"""
        # Create data loaders
        self.train_loader = TJADataLoader(train_dataset, self.config, is_training=True).create_dataloader()
        self.val_loader = TJADataLoader(val_dataset, self.config, is_training=False).create_dataloader()
        
        # Training loop
        self.model.train()
        step = 0
        
        for epoch in range(self.config.num_epochs):
            epoch_losses = []
            
            for batch_idx, batch in enumerate(self.train_loader):
                # Move batch to device
                batch = {k: v.to(self.device) if torch.is_tensor(v) else v 
                        for k, v in batch.items()}
                
                # Training step
                losses = self.training_step(batch, step)
                epoch_losses.append(losses)
                
                # Update metrics
                self.metrics.update_training_metrics(
                    step, losses, self.get_current_lr(), self.get_gradient_norm()
                )
                
                # Validation and checkpointing
                if step % self.config.validation_frequency == 0:
                    val_metrics = self.validate()
                    self.metrics.update_validation_metrics(step, val_metrics, None, None)
                    
                    # Check early stopping
                    should_continue, reason = self.strategy.should_continue_training(
                        val_metrics['total']
                    )
                    if not should_continue:
                        logging.info(f"Early stopping triggered: {reason}")
                        return self.finalize_training()
                
                if step % self.config.checkpoint_frequency == 0:
                    self.save_checkpoint(step, val_metrics if 'val_metrics' in locals() else None)
                
                step += 1
                self.strategy.current_step = step
                
                # Hardware monitoring
                if step % 100 == 0:
                    self.metrics.update_hardware_metrics(step)
        
        return self.finalize_training()
    
    def training_step(self, batch, step):
        """Single training step with gradient accumulation"""
        # Get current curriculum stage
        curriculum_stage = self.strategy.get_current_curriculum_stage()
        
        # Forward pass
        if self.scaler is not None:
            with torch.cuda.amp.autocast():
                outputs = self.model(
                    batch['audio_features'], 
                    batch['metadata'],
                    attention_mask=batch['attention_masks']
                )
                losses = self.loss_function(outputs, batch, batch['metadata'])
                loss = losses['total'] / self.config.gradient_accumulation_steps
        else:
            outputs = self.model(
                batch['audio_features'], 
                batch['metadata'],
                attention_mask=batch['attention_masks']
            )
            losses = self.loss_function(outputs, batch, batch['metadata'])
            loss = losses['total'] / self.config.gradient_accumulation_steps
        
        # Backward pass
        if self.scaler is not None:
            self.scaler.scale(loss).backward()
        else:
            loss.backward()
        
        # Optimizer step (with gradient accumulation)
        if (step + 1) % self.config.gradient_accumulation_steps == 0:
            if self.scaler is not None:
                self.scaler.unscale_(self.optimizer)
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), self.config.max_grad_norm)
                self.scaler.step(self.optimizer)
                self.scaler.update()
            else:
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), self.config.max_grad_norm)
                self.optimizer.step()
            
            self.optimizer.zero_grad()
            self.scheduler.step()
        
        return {k: v.item() if torch.is_tensor(v) else v for k, v in losses.items()}
```

## 5. Best Practices

### 5.1 Training Stability
- **Gradient Clipping**: Prevent exploding gradients in long sequences
- **Mixed Precision**: Use AMP for memory efficiency and speed
- **Warmup Scheduling**: Gradual learning rate increase for stability
- **Regularization**: Dropout, weight decay, and label smoothing

### 5.2 Monitoring and Debugging
```python
def setup_training_monitoring():
    """Setup comprehensive training monitoring"""
    # Weights & Biases integration
    import wandb
    
    wandb.init(
        project="tja-generation",
        config=training_config.__dict__,
        tags=["transformer", "sequence-generation", "music"]
    )
    
    # Custom logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('training.log'),
            logging.StreamHandler()
        ]
    )
    
    # TensorBoard logging
    from torch.utils.tensorboard import SummaryWriter
    writer = SummaryWriter('runs/tja_training')
    
    return wandb, writer
```

## 6. Challenges and Edge Cases

### 6.1 Memory Management
- **Sequence Length Variation**: Handle variable-length sequences efficiently
- **Batch Size Optimization**: Balance between memory usage and training stability
- **Gradient Accumulation**: Simulate larger batch sizes within memory constraints

### 6.2 Training Convergence
- **Loss Balancing**: Prevent any single loss component from dominating
- **Curriculum Learning**: Progressive difficulty increase for stable learning
- **Overfitting Prevention**: Early stopping and regularization strategies

### 6.3 Hardware Optimization
- **RTX 3070 Constraints**: Optimize for 8GB VRAM limitation
- **Mixed Precision**: Balance between speed and numerical stability
- **Data Loading**: Efficient data pipeline to prevent GPU starvation

## 7. Advanced Training Optimization

### 7.1 Hyperparameter Optimization
```python
class HyperparameterOptimizer:
    """Advanced hyperparameter optimization using Optuna"""

    def __init__(self, base_config: TrainingConfig):
        self.base_config = base_config
        self.study = optuna.create_study(
            direction='minimize',
            sampler=optuna.samplers.TPESampler(seed=42),
            pruner=optuna.pruners.MedianPruner(n_startup_trials=5, n_warmup_steps=1000)
        )

    def objective(self, trial):
        """Optuna objective function for hyperparameter optimization"""
        # Suggest hyperparameters
        config = self.base_config
        config.learning_rate = trial.suggest_float('learning_rate', 1e-5, 1e-3, log=True)
        config.weight_decay = trial.suggest_float('weight_decay', 1e-4, 1e-1, log=True)
        config.dropout_rate = trial.suggest_float('dropout_rate', 0.05, 0.3)
        config.label_smoothing = trial.suggest_float('label_smoothing', 0.0, 0.2)

        # Loss weight optimization
        note_weight = trial.suggest_float('note_type_weight', 0.5, 2.0)
        density_weight = trial.suggest_float('density_weight', 0.1, 1.0)
        pattern_weight = trial.suggest_float('pattern_weight', 0.1, 0.8)

        config.loss_weights = {
            'note_type': note_weight,
            'density': density_weight,
            'pattern': pattern_weight,
            'temporal': 0.2,
            'difficulty': 0.1
        }

        # Train model with suggested hyperparameters
        trainer = TJATrainer(config)
        validation_loss = trainer.train_with_early_stopping(max_steps=5000)

        # Report intermediate values for pruning
        trial.report(validation_loss, step=5000)

        if trial.should_prune():
            raise optuna.TrialPruned()

        return validation_loss

    def optimize(self, n_trials=50):
        """Run hyperparameter optimization"""
        self.study.optimize(self.objective, n_trials=n_trials)

        # Get best parameters
        best_params = self.study.best_params
        best_value = self.study.best_value

        logging.info(f"Best validation loss: {best_value}")
        logging.info(f"Best parameters: {best_params}")

        return best_params, best_value
```

### 7.2 Advanced Learning Rate Scheduling
```python
class AdaptiveLearningRateScheduler:
    """Adaptive learning rate scheduler with multiple strategies"""

    def __init__(self, optimizer, config):
        self.optimizer = optimizer
        self.config = config
        self.current_step = 0
        self.validation_losses = []
        self.best_loss = float('inf')
        self.plateau_counter = 0

        # Multiple scheduling strategies
        self.schedulers = {
            'warmup_cosine': self.create_warmup_cosine_scheduler(),
            'reduce_on_plateau': self.create_plateau_scheduler(),
            'cyclic': self.create_cyclic_scheduler()
        }

        self.current_scheduler = 'warmup_cosine'

    def create_warmup_cosine_scheduler(self):
        """Create warmup + cosine annealing scheduler"""
        def lr_lambda(step):
            if step < self.config.warmup_steps:
                return step / self.config.warmup_steps
            else:
                progress = (step - self.config.warmup_steps) / (self.config.total_training_steps - self.config.warmup_steps)
                return 0.5 * (1 + math.cos(math.pi * progress))

        return torch.optim.lr_scheduler.LambdaLR(self.optimizer, lr_lambda)

    def create_plateau_scheduler(self):
        """Create reduce on plateau scheduler"""
        return torch.optim.lr_scheduler.ReduceLROnPlateau(
            self.optimizer, mode='min', factor=0.5, patience=5, verbose=True
        )

    def create_cyclic_scheduler(self):
        """Create cyclic learning rate scheduler"""
        return torch.optim.lr_scheduler.CyclicLR(
            self.optimizer,
            base_lr=self.config.learning_rate * 0.1,
            max_lr=self.config.learning_rate,
            step_size_up=1000,
            mode='triangular2'
        )

    def step(self, validation_loss=None):
        """Step the appropriate scheduler"""
        self.current_step += 1

        if self.current_scheduler == 'warmup_cosine':
            self.schedulers['warmup_cosine'].step()

            # Switch to plateau scheduler after warmup phase
            if self.current_step > self.config.warmup_steps + 10000:
                self.current_scheduler = 'reduce_on_plateau'

        elif self.current_scheduler == 'reduce_on_plateau' and validation_loss is not None:
            self.schedulers['reduce_on_plateau'].step(validation_loss)

        elif self.current_scheduler == 'cyclic':
            self.schedulers['cyclic'].step()

    def get_current_lr(self):
        """Get current learning rate"""
        return self.optimizer.param_groups[0]['lr']
```

### 7.3 Model Ensemble and Knowledge Distillation
```python
class ModelEnsemble:
    """Ensemble of TJA generation models for improved performance"""

    def __init__(self, model_configs, ensemble_strategy='weighted_average'):
        self.models = []
        self.ensemble_strategy = ensemble_strategy
        self.model_weights = []

        # Initialize ensemble models
        for i, config in enumerate(model_configs):
            model = TJAGenerationModel(config)
            self.models.append(model)
            self.model_weights.append(1.0 / len(model_configs))  # Equal weights initially

    def train_ensemble(self, train_dataset, val_dataset):
        """Train ensemble models with different strategies"""
        ensemble_results = []

        for i, model in enumerate(self.models):
            logging.info(f"Training ensemble model {i+1}/{len(self.models)}")

            # Use different training strategies for diversity
            if i == 0:
                # Standard training
                trainer = TJATrainer(self.model_configs[i])
            elif i == 1:
                # Training with different augmentation
                config = self.model_configs[i]
                config.augmentation_probability = 0.5
                trainer = TJATrainer(config)
            elif i == 2:
                # Training with different loss weights
                config = self.model_configs[i]
                config.loss_weights['pattern'] = 0.5
                trainer = TJATrainer(config)

            # Train individual model
            results = trainer.train(train_dataset, val_dataset)
            ensemble_results.append(results)

            # Update ensemble weights based on validation performance
            val_loss = results['best_validation_loss']
            self.model_weights[i] = 1.0 / (1.0 + val_loss)

        # Normalize ensemble weights
        total_weight = sum(self.model_weights)
        self.model_weights = [w / total_weight for w in self.model_weights]

        return ensemble_results

    def ensemble_predict(self, audio_features, difficulty_levels):
        """Generate predictions using ensemble"""
        ensemble_outputs = []

        for model, weight in zip(self.models, self.model_weights):
            model.eval()
            with torch.no_grad():
                outputs = model(audio_features, difficulty_levels)

                # Weight the outputs
                weighted_outputs = {}
                for key, value in outputs.items():
                    weighted_outputs[key] = value * weight

                ensemble_outputs.append(weighted_outputs)

        # Combine ensemble outputs
        combined_outputs = {}
        for key in ensemble_outputs[0].keys():
            combined_outputs[key] = sum(output[key] for output in ensemble_outputs)

        return combined_outputs

class KnowledgeDistillation:
    """Knowledge distillation for model compression and improvement"""

    def __init__(self, teacher_model, student_model, temperature=3.0, alpha=0.7):
        self.teacher_model = teacher_model
        self.student_model = student_model
        self.temperature = temperature
        self.alpha = alpha  # Weight for distillation loss

        # Freeze teacher model
        for param in self.teacher_model.parameters():
            param.requires_grad = False
        self.teacher_model.eval()

    def distillation_loss(self, student_outputs, teacher_outputs, targets):
        """Calculate knowledge distillation loss"""
        # Standard task loss
        task_loss = self.calculate_task_loss(student_outputs, targets)

        # Distillation loss
        distill_loss = 0
        for key in student_outputs.keys():
            if key in teacher_outputs:
                student_logits = student_outputs[key] / self.temperature
                teacher_logits = teacher_outputs[key] / self.temperature

                # KL divergence between teacher and student
                kl_loss = F.kl_div(
                    F.log_softmax(student_logits, dim=-1),
                    F.softmax(teacher_logits, dim=-1),
                    reduction='batchmean'
                ) * (self.temperature ** 2)

                distill_loss += kl_loss

        # Combined loss
        total_loss = self.alpha * distill_loss + (1 - self.alpha) * task_loss

        return total_loss, task_loss, distill_loss

    def train_student(self, train_dataset, val_dataset, num_epochs=10):
        """Train student model using knowledge distillation"""
        student_trainer = TJATrainer(self.student_model)

        # Modified training loop with distillation
        for epoch in range(num_epochs):
            for batch in train_dataset:
                # Get teacher predictions
                with torch.no_grad():
                    teacher_outputs = self.teacher_model(
                        batch['audio_features'], batch['difficulty_levels']
                    )

                # Get student predictions
                student_outputs = self.student_model(
                    batch['audio_features'], batch['difficulty_levels']
                )

                # Calculate distillation loss
                total_loss, task_loss, distill_loss = self.distillation_loss(
                    student_outputs, teacher_outputs, batch
                )

                # Backward pass
                total_loss.backward()
                student_trainer.optimizer.step()
                student_trainer.optimizer.zero_grad()

                # Log losses
                if student_trainer.current_step % 100 == 0:
                    logging.info(f"Step {student_trainer.current_step}: "
                               f"Total: {total_loss:.4f}, Task: {task_loss:.4f}, "
                               f"Distill: {distill_loss:.4f}")
```

### 7.4 Advanced Regularization Techniques
```python
class AdvancedRegularization:
    """Advanced regularization techniques for TJA model training"""

    def __init__(self, model, config):
        self.model = model
        self.config = config

        # Regularization techniques
        self.techniques = {
            'spectral_normalization': config.get('spectral_norm', False),
            'weight_noise': config.get('weight_noise', False),
            'temporal_dropout': config.get('temporal_dropout', False),
            'mixup': config.get('mixup', False)
        }

    def apply_spectral_normalization(self):
        """Apply spectral normalization to linear layers"""
        for name, module in self.model.named_modules():
            if isinstance(module, nn.Linear):
                nn.utils.spectral_norm(module)

    def apply_weight_noise(self, noise_std=0.01):
        """Apply weight noise during training"""
        if self.model.training:
            for param in self.model.parameters():
                if param.requires_grad:
                    noise = torch.randn_like(param) * noise_std
                    param.data.add_(noise)

    def temporal_dropout(self, sequence, dropout_rate=0.1):
        """Apply temporal dropout to sequences"""
        if self.model.training:
            batch_size, seq_len, hidden_dim = sequence.shape

            # Create temporal dropout mask
            keep_prob = 1.0 - dropout_rate
            mask = torch.bernoulli(torch.full((batch_size, seq_len, 1), keep_prob))
            mask = mask.to(sequence.device)

            # Apply mask
            sequence = sequence * mask / keep_prob

        return sequence

    def mixup_augmentation(self, batch, alpha=0.2):
        """Apply mixup augmentation to batch"""
        if not self.model.training:
            return batch

        batch_size = batch['audio_features'].shape[0]

        # Generate mixup parameters
        lam = np.random.beta(alpha, alpha) if alpha > 0 else 1
        index = torch.randperm(batch_size)

        # Mix audio features
        mixed_audio = lam * batch['audio_features'] + (1 - lam) * batch['audio_features'][index]

        # Mix note sequences
        mixed_notes = lam * batch['note_sequences'] + (1 - lam) * batch['note_sequences'][index]

        # Create mixed batch
        mixed_batch = batch.copy()
        mixed_batch['audio_features'] = mixed_audio
        mixed_batch['note_sequences'] = mixed_notes
        mixed_batch['mixup_lambda'] = lam
        mixed_batch['mixup_index'] = index

        return mixed_batch
```

### 7.5 Training Diagnostics and Debugging
```python
class TrainingDiagnostics:
    """Comprehensive training diagnostics and debugging tools"""

    def __init__(self, model, config):
        self.model = model
        self.config = config
        self.gradient_history = []
        self.activation_history = []
        self.loss_components_history = []

    def register_hooks(self):
        """Register forward and backward hooks for diagnostics"""
        self.hooks = []

        for name, module in self.model.named_modules():
            # Forward hook for activation statistics
            forward_hook = self.create_forward_hook(name)
            self.hooks.append(module.register_forward_hook(forward_hook))

            # Backward hook for gradient statistics
            if hasattr(module, 'weight') and module.weight is not None:
                backward_hook = self.create_backward_hook(name)
                self.hooks.append(module.weight.register_hook(backward_hook))

    def create_forward_hook(self, name):
        """Create forward hook for activation monitoring"""
        def hook(module, input, output):
            if isinstance(output, torch.Tensor):
                activation_stats = {
                    'name': name,
                    'mean': output.mean().item(),
                    'std': output.std().item(),
                    'min': output.min().item(),
                    'max': output.max().item(),
                    'nan_count': torch.isnan(output).sum().item(),
                    'inf_count': torch.isinf(output).sum().item()
                }
                self.activation_history.append(activation_stats)
        return hook

    def create_backward_hook(self, name):
        """Create backward hook for gradient monitoring"""
        def hook(grad):
            if grad is not None:
                gradient_stats = {
                    'name': name,
                    'mean': grad.mean().item(),
                    'std': grad.std().item(),
                    'norm': grad.norm().item(),
                    'nan_count': torch.isnan(grad).sum().item(),
                    'inf_count': torch.isinf(grad).sum().item()
                }
                self.gradient_history.append(gradient_stats)
        return hook

    def analyze_training_health(self):
        """Analyze training health based on collected statistics"""
        health_report = {
            'gradient_health': self.analyze_gradient_health(),
            'activation_health': self.analyze_activation_health(),
            'loss_health': self.analyze_loss_health(),
            'recommendations': []
        }

        # Generate recommendations based on analysis
        health_report['recommendations'] = self.generate_recommendations(health_report)

        return health_report

    def analyze_gradient_health(self):
        """Analyze gradient health"""
        if not self.gradient_history:
            return {'status': 'no_data'}

        recent_grads = self.gradient_history[-100:]  # Last 100 gradient updates

        # Calculate statistics
        grad_norms = [g['norm'] for g in recent_grads]
        mean_norm = np.mean(grad_norms)
        std_norm = np.std(grad_norms)

        # Check for gradient problems
        vanishing_threshold = 1e-6
        exploding_threshold = 10.0

        vanishing_count = sum(1 for norm in grad_norms if norm < vanishing_threshold)
        exploding_count = sum(1 for norm in grad_norms if norm > exploding_threshold)

        return {
            'mean_norm': mean_norm,
            'std_norm': std_norm,
            'vanishing_ratio': vanishing_count / len(grad_norms),
            'exploding_ratio': exploding_count / len(grad_norms),
            'status': self.classify_gradient_health(mean_norm, vanishing_count, exploding_count, len(grad_norms))
        }

    def generate_recommendations(self, health_report):
        """Generate training recommendations based on health analysis"""
        recommendations = []

        # Gradient-based recommendations
        grad_health = health_report['gradient_health']
        if grad_health.get('vanishing_ratio', 0) > 0.1:
            recommendations.append("Consider increasing learning rate or using gradient clipping")

        if grad_health.get('exploding_ratio', 0) > 0.1:
            recommendations.append("Consider decreasing learning rate or stronger gradient clipping")

        # Activation-based recommendations
        activation_health = health_report['activation_health']
        if activation_health.get('saturation_ratio', 0) > 0.2:
            recommendations.append("Consider using different activation functions or batch normalization")

        # Loss-based recommendations
        loss_health = health_report['loss_health']
        if loss_health.get('stagnation_detected', False):
            recommendations.append("Consider adjusting loss weights or learning rate schedule")

        return recommendations

    def cleanup_hooks(self):
        """Remove all registered hooks"""
        for hook in self.hooks:
            hook.remove()
        self.hooks = []
```

## 8. Production Training Pipeline

### 8.1 Distributed Training Setup
```python
class DistributedTrainingSetup:
    """Setup for distributed training across multiple GPUs"""

    def __init__(self, config):
        self.config = config
        self.world_size = torch.cuda.device_count()
        self.rank = 0

    def setup_distributed(self):
        """Setup distributed training environment"""
        if self.world_size > 1:
            # Initialize process group
            torch.distributed.init_process_group(
                backend='nccl',
                init_method='env://',
                world_size=self.world_size,
                rank=self.rank
            )

            # Set device for current process
            torch.cuda.set_device(self.rank)

            return True
        return False

    def wrap_model_for_distributed(self, model):
        """Wrap model for distributed training"""
        if self.world_size > 1:
            model = torch.nn.parallel.DistributedDataParallel(
                model,
                device_ids=[self.rank],
                output_device=self.rank,
                find_unused_parameters=True
            )
        return model

    def create_distributed_sampler(self, dataset):
        """Create distributed sampler for dataset"""
        if self.world_size > 1:
            return torch.utils.data.distributed.DistributedSampler(
                dataset,
                num_replicas=self.world_size,
                rank=self.rank,
                shuffle=True
            )
        return None
```

### 8.2 Checkpoint Management
```python
class CheckpointManager:
    """Advanced checkpoint management with versioning and recovery"""

    def __init__(self, checkpoint_dir, max_checkpoints=5):
        self.checkpoint_dir = Path(checkpoint_dir)
        self.checkpoint_dir.mkdir(parents=True, exist_ok=True)
        self.max_checkpoints = max_checkpoints

    def save_checkpoint(self, model, optimizer, scheduler, step, metrics, is_best=False):
        """Save training checkpoint with metadata"""
        checkpoint = {
            'step': step,
            'model_state_dict': model.state_dict(),
            'optimizer_state_dict': optimizer.state_dict(),
            'scheduler_state_dict': scheduler.state_dict(),
            'metrics': metrics,
            'timestamp': datetime.now().isoformat(),
            'config': self.config.__dict__ if hasattr(self, 'config') else {}
        }

        # Save regular checkpoint
        checkpoint_path = self.checkpoint_dir / f"checkpoint_step_{step}.pt"
        torch.save(checkpoint, checkpoint_path)

        # Save best checkpoint
        if is_best:
            best_path = self.checkpoint_dir / "best_checkpoint.pt"
            torch.save(checkpoint, best_path)

        # Cleanup old checkpoints
        self.cleanup_old_checkpoints()

        logging.info(f"Checkpoint saved: {checkpoint_path}")

    def load_checkpoint(self, model, optimizer=None, scheduler=None, checkpoint_path=None):
        """Load checkpoint and restore training state"""
        if checkpoint_path is None:
            # Find latest checkpoint
            checkpoint_files = list(self.checkpoint_dir.glob("checkpoint_step_*.pt"))
            if not checkpoint_files:
                logging.info("No checkpoints found")
                return 0

            checkpoint_path = max(checkpoint_files, key=lambda x: int(x.stem.split('_')[-1]))

        checkpoint = torch.load(checkpoint_path, map_location='cpu')

        # Load model state
        model.load_state_dict(checkpoint['model_state_dict'])

        # Load optimizer state
        if optimizer is not None and 'optimizer_state_dict' in checkpoint:
            optimizer.load_state_dict(checkpoint['optimizer_state_dict'])

        # Load scheduler state
        if scheduler is not None and 'scheduler_state_dict' in checkpoint:
            scheduler.load_state_dict(checkpoint['scheduler_state_dict'])

        step = checkpoint['step']
        metrics = checkpoint.get('metrics', {})

        logging.info(f"Checkpoint loaded: {checkpoint_path}, step: {step}")

        return step, metrics

    def cleanup_old_checkpoints(self):
        """Remove old checkpoints to save disk space"""
        checkpoint_files = sorted(
            self.checkpoint_dir.glob("checkpoint_step_*.pt"),
            key=lambda x: int(x.stem.split('_')[-1])
        )

        if len(checkpoint_files) > self.max_checkpoints:
            for old_checkpoint in checkpoint_files[:-self.max_checkpoints]:
                old_checkpoint.unlink()
                logging.info(f"Removed old checkpoint: {old_checkpoint}")
```

---

**Phase 5 Completion Criteria:**
- [ ] Complete training pipeline with curriculum learning and adaptive strategies
- [ ] Comprehensive metrics tracking with TJA-specific evaluations
- [ ] RTX 3070-optimized training with <7GB memory usage and mixed precision
- [ ] Advanced augmentation, regularization, and ensemble techniques
- [ ] Hyperparameter optimization with Optuna integration
- [ ] Early stopping, checkpoint management, and distributed training support
- [ ] Training diagnostics and debugging tools with health monitoring
- [ ] Knowledge distillation and model compression capabilities
- [ ] Production-ready training pipeline with monitoring and recovery

## 8. Success Criteria and Resource Efficiency Targets

### 8.1 Hardware Resource Efficiency Requirements (Mandatory)
```python
PHASE_5_RESOURCE_EFFICIENCY_TARGETS = {
    "gpu_training_utilization": {
        "sustained_gpu_utilization": 0.88,        # 88% sustained GPU utilization
        "vram_efficiency": 0.85,                  # 85% VRAM efficiency
        "compute_unit_occupancy": 0.90,           # 90% compute unit occupancy
        "tensor_core_utilization": 0.85,         # 85% tensor core utilization
        "gpu_memory_bandwidth_utilization": 0.88, # 88% GPU memory bandwidth usage
        "training_throughput_efficiency": 0.82    # 82% training throughput efficiency
    },
    "memory_training_efficiency": {
        "memory_utilization": 0.85,               # 85% memory utilization
        "gradient_memory_efficiency": 0.88,       # 88% gradient memory efficiency
        "batch_memory_efficiency": 0.90,          # 90% batch memory efficiency
        "cache_hit_ratio": 0.90,                  # 90% cache hit ratio
        "memory_fragmentation": 0.05,             # <5% memory fragmentation
        "optimizer_memory_efficiency": 0.85       # 85% optimizer memory efficiency
    },
    "training_process_efficiency": {
        "sustained_compute_occupancy": 0.75,      # 75% sustained compute occupancy
        "gradient_computation_efficiency": 0.88,  # 88% gradient computation efficiency
        "backpropagation_efficiency": 0.85,       # 85% backpropagation efficiency
        "parameter_update_efficiency": 0.90,      # 90% parameter update efficiency
        "mixed_precision_efficiency": 0.88        # 88% mixed precision efficiency
    },
    "model_quality": {
        "training_stability": 0.95,               # 95% training stability
        "convergence_efficiency": 0.85,           # 85% convergence efficiency
        "note_accuracy": 0.90,                    # 90% note accuracy
        "pattern_loss_threshold": 0.5,            # <0.5 pattern loss
        "difficulty_consistency": 0.85            # 85% difficulty consistency
    }
}
```

**Estimated Timeline:** 4-6 weeks (including hyperparameter tuning and training time)
**Resource Requirements:** GPU-intensive with optimized resource utilization, 200GB+ storage, monitoring infrastructure
**Critical Dependencies:** Resource efficiency implementation, sustained utilization monitoring

# Phase 6: Inference Pipeline and Validation - Request for Proposal (RFP)
## Self-Contained Implementation Specification

## 1. Phase Overview and System Context

### 1.1 Project Objective
Implement a **TJA rhythm chart generation system** that creates high-quality Taiko no Tatsufin charts from audio input using deep learning. This system analyzes audio files and generates appropriate note sequences for difficulty levels 8-10 (Oni/Edit courses).

### 1.2 Phase 6 Purpose and Role
Phase 6 implements the complete inference pipeline for TJA chart generation, transforming the trained model from Phase 5 into a production-ready system. This phase creates end-to-end workflows for generating high-quality TJA charts from audio input, including preprocessing, model inference, post-processing, and comprehensive validation against human-created charts.

### 1.3 Implementation Status and Performance
**Status**: 📋 **PLANNED** - Production components organized

**Target Performance Metrics**:
- **Generation Speed**: <5 seconds per chart
- **Memory Usage**: <4GB RAM during inference
- **Chart Quality**: >80% human similarity score
- **Validation Success**: >95% format compliance

**Key Implementation Files**:
- `src/phase_6/controller.py` - Main phase controller
- `src/phase_6/inference_system.py` - Production inference system
- `src/phase_6/audio_preprocessing.py` - Real-time audio processing
- `src/phase_6/tja_postprocessing.py` - TJA format validation
- `src/phase_6/validation_framework.py` - Comprehensive validation
- `src/phase_6/performance_benchmark.py` - Performance monitoring

### 1.4 Hardware Environment (Verified)
**CRITICAL**: All implementations must be optimized for the verified hardware environment:

```python
HARDWARE_SPECS = {
    "gpu": {
        "model": "NVIDIA GeForce RTX 3070",
        "vram_gb": 8.0,
        "cuda_version": "12.1",
        "pytorch_version": "2.5.1+cu121",
        "memory_constraint": "6.8GB usable (safety margin)",
        "optimization_target": "inference_optimization"
    },
    "cpu": {
        "physical_cores": 8,
        "logical_cores": 16,
        "recommended_workers": 8,  # Full utilization for inference
        "optimization_target": "real_time_processing"
    },
    "memory": {
        "total_ram_gb": 31.8,
        "available_ram_gb": 28.0,
        "recommended_cache_gb": 16,  # Full cache for inference
        "inference_memory_gb": 8     # Reserved for inference pipeline
    }
}
```

### 1.5 Phase Dependencies and Data Flow
- **Previous Phase**: Phase 5 (Model Training) - requires trained model weights and validation metrics
- **Next Phase**: Final deployment and user interface (if applicable)
- **Input Contract**: Trained model weights and validation datasets from Phase 5
- **Output Contract**: Production-ready TJA generation system
- **Data Flow**: Raw audio input → Inference pipeline → Generated TJA charts → Validation output

## 2. Detailed Specification

### 2.1 Input Requirements
- **Trained Model**: Best checkpoint from Phase 5 with validation metrics
- **Audio Input**: `.ogg` files with metadata (BPM, offset)
- **Target Parameters**: Difficulty level (8, 9, or 10), course type (oni/edit)
- **Validation Dataset**: Hold-out test set for comprehensive evaluation

### 2.2 Inference Pipeline Architecture

#### 2.2.1 End-to-End Inference System
```python
class TJAInferenceSystem:
    """Complete TJA chart generation inference system"""
    
    def __init__(self, model_path: str, config: InferenceConfig):
        self.config = config
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
        # Load trained model
        self.model = self.load_model(model_path)
        self.model.eval()
        
        # Initialize preprocessing pipeline
        self.preprocessor = AudioPreprocessor(config.preprocessing)
        
        # Initialize post-processing pipeline
        self.postprocessor = TJAPostProcessor(config.postprocessing)
        
        # Initialize validation framework
        self.validator = TJAValidator(config.validation)
        
        # Performance monitoring
        self.performance_monitor = InferencePerformanceMonitor()
    
    def generate_chart(self, audio_path: str, bpm: float, offset: float, 
                      difficulty_level: int, course_type: str = "oni") -> Dict:
        """Generate TJA chart from audio input"""
        
        start_time = time.time()
        
        try:
            # Step 1: Preprocess audio
            preprocessing_start = time.time()
            audio_features, timing_grid = self.preprocessor.process_audio(
                audio_path, bpm, offset
            )
            preprocessing_time = time.time() - preprocessing_start
            
            # Step 2: Model inference
            inference_start = time.time()
            with torch.no_grad():
                model_outputs = self.model_inference(
                    audio_features, timing_grid, difficulty_level
                )
            inference_time = time.time() - inference_start
            
            # Step 3: Post-processing
            postprocessing_start = time.time()
            tja_chart = self.postprocessor.generate_tja(
                model_outputs, timing_grid, bpm, offset, difficulty_level, course_type
            )
            postprocessing_time = time.time() - postprocessing_start
            
            # Step 4: Validation
            validation_start = time.time()
            validation_results = self.validator.validate_generated_chart(tja_chart)
            validation_time = time.time() - validation_start
            
            total_time = time.time() - start_time
            
            # Performance metrics
            performance_metrics = {
                "total_time": total_time,
                "preprocessing_time": preprocessing_time,
                "inference_time": inference_time,
                "postprocessing_time": postprocessing_time,
                "validation_time": validation_time,
                "audio_duration": len(audio_features) / 50.0,  # 50 FPS
                "realtime_factor": (len(audio_features) / 50.0) / total_time
            }
            
            return {
                "tja_chart": tja_chart,
                "validation_results": validation_results,
                "performance_metrics": performance_metrics,
                "model_outputs": model_outputs,
                "success": True
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "traceback": traceback.format_exc()
            }
    
    def model_inference(self, audio_features: torch.Tensor, timing_grid: Dict, 
                       difficulty_level: int) -> Dict:
        """Perform model inference with optimizations"""
        
        # Prepare inputs
        audio_features = audio_features.unsqueeze(0).to(self.device)  # Add batch dimension
        difficulty_tensor = torch.tensor([difficulty_level - 8]).to(self.device)  # 0, 1, 2 for levels 8, 9, 10
        
        # Create attention mask for variable length sequences
        seq_len = audio_features.shape[1]
        attention_mask = torch.zeros(1, seq_len, dtype=torch.bool).to(self.device)
        
        # Model inference with memory optimization
        if self.config.use_chunked_inference and seq_len > self.config.max_chunk_size:
            outputs = self.chunked_inference(audio_features, difficulty_tensor, attention_mask)
        else:
            outputs = self.model(audio_features, difficulty_tensor, attention_mask=attention_mask)
        
        # Move outputs to CPU and convert to numpy for post-processing
        cpu_outputs = {}
        for key, value in outputs.items():
            if torch.is_tensor(value):
                cpu_outputs[key] = value.squeeze(0).cpu()  # Remove batch dimension
            else:
                cpu_outputs[key] = value
        
        return cpu_outputs
    
    def chunked_inference(self, audio_features: torch.Tensor, difficulty_tensor: torch.Tensor,
                         attention_mask: torch.Tensor) -> Dict:
        """Perform inference on long sequences using chunking"""
        
        seq_len = audio_features.shape[1]
        chunk_size = self.config.max_chunk_size
        overlap = self.config.chunk_overlap
        
        chunked_outputs = []
        
        for start_idx in range(0, seq_len, chunk_size - overlap):
            end_idx = min(start_idx + chunk_size, seq_len)
            
            # Extract chunk
            chunk_audio = audio_features[:, start_idx:end_idx]
            chunk_mask = attention_mask[:, start_idx:end_idx]
            
            # Inference on chunk
            chunk_outputs = self.model(chunk_audio, difficulty_tensor, attention_mask=chunk_mask)
            
            # Handle overlap
            if start_idx > 0:
                # Blend overlapping regions
                overlap_start = overlap // 2
                for key, value in chunk_outputs.items():
                    if torch.is_tensor(value) and len(value.shape) > 1:
                        # Apply fade-in to current chunk
                        fade_in = torch.linspace(0, 1, overlap_start).unsqueeze(-1)
                        value[:, :overlap_start] *= fade_in.to(value.device)
                        
                        # Apply fade-out to previous chunk
                        if len(chunked_outputs) > 0:
                            prev_value = chunked_outputs[-1][key]
                            fade_out = torch.linspace(1, 0, overlap_start).unsqueeze(-1)
                            prev_value[:, -overlap_start:] *= fade_out.to(prev_value.device)
                            
                            # Blend overlapping regions
                            value[:, :overlap_start] += prev_value[:, -overlap_start:]
            
            chunked_outputs.append(chunk_outputs)
        
        # Concatenate chunks
        final_outputs = {}
        for key in chunked_outputs[0].keys():
            if torch.is_tensor(chunked_outputs[0][key]):
                # Handle overlapping concatenation
                concatenated_values = []
                for i, chunk_output in enumerate(chunked_outputs):
                    value = chunk_output[key]
                    if i > 0:
                        # Remove overlap from all chunks except the first
                        value = value[:, overlap//2:]
                    if i < len(chunked_outputs) - 1:
                        # Remove overlap from all chunks except the last
                        value = value[:, :-overlap//2]
                    concatenated_values.append(value)
                
                final_outputs[key] = torch.cat(concatenated_values, dim=1)
            else:
                final_outputs[key] = chunked_outputs[0][key]
        
        return final_outputs

@dataclass
class InferenceConfig:
    """Configuration for inference pipeline"""
    
    # Model settings
    model_path: str = "checkpoints/best_checkpoint.pt"
    device: str = "auto"  # "auto", "cuda", "cpu"
    
    # Chunked inference settings
    use_chunked_inference: bool = True
    max_chunk_size: int = 2000  # frames
    chunk_overlap: int = 200    # frames
    
    # Preprocessing settings
    preprocessing: Dict = field(default_factory=lambda: {
        "sample_rate": 44100,
        "frame_rate": 50,
        "normalize_audio": True,
        "apply_preemphasis": True
    })
    
    # Post-processing settings
    postprocessing: Dict = field(default_factory=lambda: {
        "note_threshold": 0.5,
        "density_smoothing": True,
        "pattern_coherence_check": True,
        "quantization_enabled": True,
        "minimum_note_gap": 0.05  # seconds
    })
    
    # Validation settings
    validation: Dict = field(default_factory=lambda: {
        "check_timing_consistency": True,
        "check_difficulty_appropriateness": True,
        "check_musical_coherence": True,
        "generate_quality_score": True
    })
    
    # Performance settings
    batch_size: int = 1
    num_workers: int = 2
    pin_memory: bool = True
```

#### 2.2.2 Audio Preprocessing Pipeline
```python
class AudioPreprocessor:
    """Optimized audio preprocessing for inference"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.sample_rate = config["sample_rate"]
        self.frame_rate = config["frame_rate"]
        
        # Initialize feature extractors
        self.spectral_extractor = SpectralFeatureExtractor(config)
        self.rhythmic_extractor = RhythmicFeatureExtractor(config)
        self.temporal_extractor = TemporalFeatureExtractor(config)
    
    def process_audio(self, audio_path: str, bpm: float, offset: float) -> Tuple[torch.Tensor, Dict]:
        """Process audio file into model-ready features"""
        
        # Load audio
        audio, sr = librosa.load(audio_path, sr=self.sample_rate)
        
        # Apply preprocessing
        if self.config.get("normalize_audio", True):
            audio = librosa.util.normalize(audio)
        
        if self.config.get("apply_preemphasis", True):
            audio = librosa.effects.preemphasis(audio)
        
        # Extract features
        spectral_features = self.spectral_extractor.extract(audio, sr)
        rhythmic_features = self.rhythmic_extractor.extract(audio, sr)
        temporal_features = self.temporal_extractor.extract(audio, sr)
        
        # Combine features
        combined_features = torch.cat([
            spectral_features,
            rhythmic_features,
            temporal_features
        ], dim=-1)
        
        # Create timing grid
        timing_grid = self.create_timing_grid(len(audio), bmp, offset, sr)
        
        return combined_features, timing_grid
    
    def create_timing_grid(self, audio_length: int, bpm: float, offset: float, sr: int) -> Dict:
        """Create timing grid aligned with audio features"""
        
        duration = audio_length / sr
        num_frames = int(duration * self.frame_rate)
        
        # Time axis
        time_axis = torch.linspace(0, duration, num_frames)
        
        # Adjust for offset
        adjusted_time = time_axis - offset
        
        # Beat grid
        beat_duration = 60.0 / bpm
        beat_grid = torch.zeros(num_frames)
        measure_grid = torch.zeros(num_frames)
        
        for i, t in enumerate(adjusted_time):
            if t >= 0:
                beat_phase = (t % beat_duration) / beat_duration
                beat_grid[i] = 1.0 - beat_phase
                
                measure_phase = (t % (beat_duration * 4)) / (beat_duration * 4)
                measure_grid[i] = 1.0 - measure_phase
        
        return {
            "time_axis": time_axis,
            "beat_grid": beat_grid,
            "measure_grid": measure_grid,
            "bpm": torch.full((num_frames,), bpm),
            "offset": offset
        }
```

#### 2.2.3 TJA Post-Processing Pipeline
```python
class TJAPostProcessor:
    """Convert model outputs to valid TJA format"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.note_threshold = config.get("note_threshold", 0.5)
        self.minimum_note_gap = config.get("minimum_note_gap", 0.05)
        
        # TJA format specifications
        self.note_type_mapping = {
            0: "0",  # Empty
            1: "1",  # Don (red)
            2: "2",  # Ka (blue)
            3: "3",  # Big don
            4: "4",  # Big ka
            5: "5",  # Drumroll start
            6: "6",  # Big drumroll start
            7: "7"   # Balloon start
        }
    
    def generate_tja(self, model_outputs: Dict, timing_grid: Dict, bpm: float, 
                    offset: float, difficulty_level: int, course_type: str) -> str:
        """Generate complete TJA file from model outputs"""
        
        # Extract note predictions
        note_predictions = model_outputs["note_types"]
        note_probabilities = torch.softmax(note_predictions, dim=-1)
        
        # Apply thresholding and post-processing
        processed_notes = self.process_note_predictions(note_probabilities, timing_grid)
        
        # Apply quantization to align with musical timing
        if self.config.get("quantization_enabled", True):
            processed_notes = self.quantize_notes(processed_notes, timing_grid, bpm)
        
        # Apply pattern coherence checks
        if self.config.get("pattern_coherence_check", True):
            processed_notes = self.apply_pattern_coherence(processed_notes, model_outputs)
        
        # Generate TJA format string
        tja_content = self.format_as_tja(
            processed_notes, timing_grid, bpm, offset, difficulty_level, course_type
        )
        
        return tja_content
    
    def process_note_predictions(self, note_probabilities: torch.Tensor, 
                               timing_grid: Dict) -> List[Dict]:
        """Process raw note predictions into structured note events"""
        
        processed_notes = []
        last_note_time = -self.minimum_note_gap
        
        for frame_idx, frame_probs in enumerate(note_probabilities):
            # Get most likely note type
            note_type = torch.argmax(frame_probs).item()
            confidence = frame_probs[note_type].item()
            
            # Skip empty notes or low-confidence predictions
            if note_type == 0 or confidence < self.note_threshold:
                continue
            
            # Calculate timing
            current_time = timing_grid["time_axis"][frame_idx].item()
            
            # Enforce minimum note gap
            if current_time - last_note_time < self.minimum_note_gap:
                continue
            
            # Create note event
            note_event = {
                "time": current_time,
                "frame_idx": frame_idx,
                "note_type": note_type,
                "confidence": confidence,
                "beat_position": timing_grid["beat_grid"][frame_idx].item(),
                "measure_position": timing_grid["measure_grid"][frame_idx].item()
            }
            
            processed_notes.append(note_event)
            last_note_time = current_time
        
        return processed_notes
    
    def quantize_notes(self, notes: List[Dict], timing_grid: Dict, bpm: float) -> List[Dict]:
        """Quantize note timings to musical grid"""
        
        beat_duration = 60.0 / bpm
        quantization_levels = [1/16, 1/12, 1/8, 1/6, 1/4, 1/3, 1/2, 1]  # Musical subdivisions
        
        quantized_notes = []
        
        for note in notes:
            original_time = note["time"]
            
            # Find closest quantization point
            best_quantized_time = original_time
            min_error = float('inf')
            
            # Check each quantization level
            for subdivision in quantization_levels:
                grid_interval = beat_duration * subdivision
                quantized_time = round(original_time / grid_interval) * grid_interval
                error = abs(original_time - quantized_time)
                
                if error < min_error:
                    min_error = error
                    best_quantized_time = quantized_time
            
            # Only quantize if the error is reasonable (< 50ms)
            if min_error < 0.05:
                note["time"] = best_quantized_time
                note["quantized"] = True
                note["quantization_error"] = min_error
            else:
                note["quantized"] = False
                note["quantization_error"] = min_error
            
            quantized_notes.append(note)
        
        return quantized_notes
    
    def format_as_tja(self, notes: List[Dict], timing_grid: Dict, bpm: float, 
                     offset: float, difficulty_level: int, course_type: str) -> str:
        """Format processed notes as TJA file content"""
        
        # TJA header
        tja_lines = [
            f"TITLE:Generated Chart",
            f"BPM:{bpm}",
            f"OFFSET:{offset}",
            f"",
            f"COURSE:{course_type.capitalize()}",
            f"LEVEL:{difficulty_level}",
            f"BALLOON:",  # Will be filled if balloon notes are present
            f"SCOREINIT:300",
            f"SCOREDIFF:100",
            f"",
            f"#START"
        ]
        
        # Convert notes to TJA notation
        if not notes:
            tja_lines.extend(["0,", "#END"])
            return "\n".join(tja_lines)
        
        # Group notes by measures
        beat_duration = 60.0 / bmp
        measure_duration = beat_duration * 4
        
        current_measure_start = 0
        current_measure_notes = []
        
        for note in notes:
            note_time = note["time"]
            measure_number = int(note_time / measure_duration)
            
            # Start new measure if needed
            while measure_number > current_measure_start:
                # Finish current measure
                if current_measure_notes:
                    measure_line = self.format_measure(current_measure_notes, measure_duration)
                    tja_lines.append(measure_line)
                else:
                    tja_lines.append("0,")  # Empty measure
                
                current_measure_start += 1
                current_measure_notes = []
            
            current_measure_notes.append(note)
        
        # Finish last measure
        if current_measure_notes:
            measure_line = self.format_measure(current_measure_notes, measure_duration)
            tja_lines.append(measure_line)
        
        tja_lines.append("#END")
        
        return "\n".join(tja_lines)
    
    def format_measure(self, measure_notes: List[Dict], measure_duration: float) -> str:
        """Format notes within a single measure"""
        
        if not measure_notes:
            return "0,"
        
        # Determine measure resolution based on note density
        num_notes = len(measure_notes)
        if num_notes <= 4:
            resolution = 4
        elif num_notes <= 8:
            resolution = 8
        elif num_notes <= 16:
            resolution = 16
        else:
            resolution = 32
        
        # Create measure grid
        measure_grid = ["0"] * resolution
        subdivision_duration = measure_duration / resolution
        
        # Place notes in grid
        for note in measure_notes:
            relative_time = note["time"] % measure_duration
            grid_position = int(relative_time / subdivision_duration)
            grid_position = min(grid_position, resolution - 1)  # Clamp to valid range
            
            note_symbol = self.note_type_mapping[note["note_type"]]
            measure_grid[grid_position] = note_symbol
        
        return "".join(measure_grid) + ","
```

## 3. Output Path Specifications

### 3.1 Standardized Directory Structure
Phase 6 outputs are organized within the standardized `data/phase_6/` directory structure:

```
data/phase_6/
├── outputs/                   # Primary phase outputs
│   ├── inference_results/    # Generated TJA charts
│   │   ├── generated_charts/     # Final TJA chart files
│   │   ├── intermediate_outputs/ # Intermediate generation results
│   │   ├── quality_scores/       # Chart quality assessments
│   │   └── validation_charts/    # Charts for validation testing
│   ├── validation_reports/   # Quality assessment reports
│   │   ├── chart_quality_report.json
│   │   ├── human_similarity_scores.json
│   │   └── format_compliance_report.json
│   ├── performance_benchmarks.json # System performance metrics
│   └── api_documentation.json      # API interface documentation
├── metadata/                 # Processing metadata
│   ├── inference_statistics.json
│   ├── generation_parameters.json
│   └── model_performance.json
├── validation/               # Validation results
│   ├── inference_validation_report.json
│   ├── chart_quality_validation.json
│   └── system_performance_validation.json
├── logs/                     # Phase-specific logs
│   ├── phase6_inference.log
│   ├── chart_generation.log
│   └── validation_tests.log
└── temp/                     # Temporary processing files
    ├── inference_cache/
    └── validation_temp/
```

### 3.2 Output Schema Specification
Phase 6 implements the `Phase6Output` schema extending the base `StandardizedOutput`:

```python
@dataclass
class Phase6Output(StandardizedOutput):
    phase_number: int = 6
    phase_name: str = "Inference Pipeline and Validation"

    # Phase-specific outputs
    charts_generated: int                    # Number of charts generated
    average_generation_time_seconds: float   # Average generation time
    quality_score: float                     # Overall quality score
    validation_passed: bool                  # Validation success status

    # File paths
    inference_results_dir: str = "data/phase_6/outputs/inference_results/"
    validation_reports_dir: str = "data/phase_6/outputs/validation_reports/"
    performance_benchmarks_path: str = "data/phase_6/outputs/performance_benchmarks.json"
```

### 3.3 Data Contract for Production Deployment
Phase 6 outputs must satisfy the following contract for production deployment:

**Required Outputs**:
- `inference_results/generated_charts/`: High-quality TJA charts ready for gameplay
- `validation_reports/`: Comprehensive quality assessment and validation results
- `performance_benchmarks.json`: System performance metrics and optimization data
- `api_documentation.json`: Complete API interface documentation

**Data Quality Requirements**:
- **Generation Speed**: <5 seconds per chart generation
- **Chart Quality**: >80% human similarity score
- **Format Compliance**: >95% TJA format compliance
- **System Performance**: <4GB RAM usage during inference

### 3.4 Validation Requirements
All Phase 6 outputs undergo comprehensive validation:

```python
PHASE_6_VALIDATION_REQUIREMENTS = {
    "file_existence": {
        "inference_results/generated_charts/": "required_directory",
        "validation_reports/": "required_directory",
        "performance_benchmarks.json": "required",
        "api_documentation.json": "required"
    },
    "chart_validation": {
        "tja_format_compliance": ">0.95",
        "playability_score": ">0.80",
        "difficulty_appropriateness": ">0.85"
    },
    "performance_thresholds": {
        "generation_time_seconds": "<5.0",
        "memory_usage_gb": "<4.0",
        "quality_score": ">0.80"
    }
}
```

### 3.5 Cross-Reference
This specification aligns with:
- **Previous Phase**: [Phase 5 Model Training Optimization RFP](Phase_5_Model_Training_Optimization_RFP.md) - Output Contract
- **Production Deployment**: Final system ready for production use
- **Path Management**: Standardized path resolution via `PathManager.get_phase_output_path(6)`

## 4. Small-Scale Test First

### 4.1 Inference Pipeline Validation
- **Test Songs**: 10 diverse songs from validation set
- **Performance Benchmarks**: Measure inference speed and memory usage
- **Quality Assessment**: Compare generated charts with ground truth
- **Edge Case Testing**: Handle various audio lengths and BPM ranges

### 3.2 Validation Framework
```python
def validate_inference_pipeline():
    """Comprehensive validation of inference pipeline"""
    
    test_cases = [
        {"audio": "test_song_1.ogg", "bpm": 120, "offset": -1.5, "difficulty": 8},
        {"audio": "test_song_2.ogg", "bpm": 180, "offset": -2.1, "difficulty": 9},
        {"audio": "test_song_3.ogg", "bpm": 90, "offset": -0.8, "difficulty": 10},
    ]
    
    inference_system = TJAInferenceSystem("best_checkpoint.pt", InferenceConfig())
    
    results = []
    for test_case in test_cases:
        result = inference_system.generate_chart(
            test_case["audio"], test_case["bpm"], 
            test_case["offset"], test_case["difficulty"]
        )
        results.append(result)
    
    # Analyze results
    performance_summary = analyze_performance_results(results)
    quality_summary = analyze_quality_results(results)
    
    return {
        "performance": performance_summary,
        "quality": quality_summary,
        "individual_results": results
    }
```

### 3.3 Expected Test Results
- **Inference Speed**: <5 seconds for 3-minute song on RTX 3070
- **Memory Usage**: <4GB GPU memory during inference
- **Realtime Factor**: >10x (process 3-minute song in <18 seconds)
- **Chart Quality**: >80% similarity to human-created charts

## 4. Implementation Guidelines

### 4.1 Performance Optimization
```python
class InferenceOptimizer:
    """Optimize inference performance for production use"""
    
    def __init__(self, model, config):
        self.model = model
        self.config = config
        
        # Apply optimizations
        self.optimize_model()
    
    def optimize_model(self):
        """Apply various model optimizations"""
        
        # Model compilation (PyTorch 2.0)
        if hasattr(torch, 'compile') and self.config.get('compile_model', True):
            self.model = torch.compile(self.model, mode='reduce-overhead')
        
        # TensorRT optimization (if available)
        if self.config.get('use_tensorrt', False):
            self.model = self.optimize_with_tensorrt()
        
        # ONNX export for deployment
        if self.config.get('export_onnx', False):
            self.export_to_onnx()
    
    def optimize_with_tensorrt(self):
        """Optimize model with TensorRT"""
        try:
            import torch_tensorrt
            
            # Compile model with TensorRT
            compiled_model = torch_tensorrt.compile(
                self.model,
                inputs=[
                    torch_tensorrt.Input(shape=[1, 2000, 240]),  # Audio features
                    torch_tensorrt.Input(shape=[1], dtype=torch.long)  # Difficulty
                ],
                enabled_precisions={torch.float, torch.half}
            )
            
            return compiled_model
            
        except ImportError:
            logging.warning("TensorRT not available, skipping optimization")
            return self.model
```

## 5. Best Practices

### 5.1 Error Handling and Robustness
- **Input Validation**: Comprehensive validation of audio files and parameters
- **Graceful Degradation**: Fallback strategies for edge cases
- **Memory Management**: Efficient cleanup and garbage collection
- **Logging**: Detailed logging for debugging and monitoring

### 5.2 Quality Assurance
```python
class QualityAssurance:
    """Quality assurance for generated TJA charts"""
    
    def __init__(self):
        self.quality_metrics = [
            self.check_timing_consistency,
            self.check_note_density,
            self.check_pattern_coherence,
            self.check_difficulty_appropriateness
        ]
    
    def assess_chart_quality(self, tja_chart: str, reference_data: Dict) -> Dict:
        """Comprehensive quality assessment"""
        
        quality_scores = {}
        
        for metric_func in self.quality_metrics:
            try:
                score = metric_func(tja_chart, reference_data)
                quality_scores[metric_func.__name__] = score
            except Exception as e:
                logging.error(f"Quality metric {metric_func.__name__} failed: {e}")
                quality_scores[metric_func.__name__] = 0.0
        
        # Calculate overall quality score
        overall_score = np.mean(list(quality_scores.values()))
        quality_scores['overall_quality'] = overall_score
        
        return quality_scores
```

## 6. Challenges and Edge Cases

### 6.1 Audio Processing Challenges
- **Variable Audio Quality**: Handle different encoding qualities and sample rates
- **Long Audio Files**: Efficient processing of songs >10 minutes
- **Silence Detection**: Handle intro/outro silence appropriately
- **Audio Artifacts**: Robust handling of compression artifacts

### 6.2 Model Output Processing
- **Confidence Calibration**: Proper thresholding for note predictions
- **Temporal Consistency**: Maintain consistent timing across long sequences
- **Pattern Validation**: Ensure generated patterns are musically coherent
- **Difficulty Scaling**: Appropriate note density for target difficulty

## 7. Comprehensive Validation Framework

### 7.1 TJA Chart Validator
```python
class TJAValidator:
    """Comprehensive validation of generated TJA charts"""

    def __init__(self, config: Dict):
        self.config = config

        # Initialize validation components
        self.format_validator = TJAFormatValidator()
        self.musical_validator = MusicalCoherenceValidator()
        self.difficulty_validator = DifficultyValidator()
        self.timing_validator = TimingValidator()

        # Reference data for comparison
        self.reference_patterns = self.load_reference_patterns()
        self.difficulty_benchmarks = self.load_difficulty_benchmarks()

    def validate_generated_chart(self, tja_content: str, reference_data: Dict = None) -> Dict:
        """Perform comprehensive validation of generated TJA chart"""

        validation_results = {
            "format_validation": self.format_validator.validate(tja_content),
            "musical_validation": self.musical_validator.validate(tja_content, reference_data),
            "difficulty_validation": self.difficulty_validator.validate(tja_content, reference_data),
            "timing_validation": self.timing_validator.validate(tja_content, reference_data),
            "overall_score": 0.0,
            "recommendations": []
        }

        # Calculate overall validation score
        validation_results["overall_score"] = self.calculate_overall_score(validation_results)

        # Generate recommendations
        validation_results["recommendations"] = self.generate_recommendations(validation_results)

        return validation_results

    def calculate_overall_score(self, validation_results: Dict) -> float:
        """Calculate weighted overall validation score"""

        weights = {
            "format_validation": 0.2,
            "musical_validation": 0.3,
            "difficulty_validation": 0.3,
            "timing_validation": 0.2
        }

        weighted_score = 0.0
        total_weight = 0.0

        for category, weight in weights.items():
            if category in validation_results and "score" in validation_results[category]:
                weighted_score += validation_results[category]["score"] * weight
                total_weight += weight

        return weighted_score / total_weight if total_weight > 0 else 0.0

class MusicalCoherenceValidator:
    """Validate musical coherence of generated charts"""

    def __init__(self):
        self.pattern_analyzer = PatternAnalyzer()
        self.rhythm_analyzer = RhythmAnalyzer()
        self.flow_analyzer = FlowAnalyzer()

    def validate(self, tja_content: str, reference_data: Dict = None) -> Dict:
        """Validate musical coherence"""

        # Parse TJA content
        parsed_chart = self.parse_tja_content(tja_content)

        coherence_metrics = {
            "pattern_coherence": self.pattern_analyzer.analyze_coherence(parsed_chart),
            "rhythmic_consistency": self.rhythm_analyzer.analyze_consistency(parsed_chart),
            "flow_quality": self.flow_analyzer.analyze_flow(parsed_chart),
            "transition_smoothness": self.analyze_transitions(parsed_chart),
            "musical_structure": self.analyze_musical_structure(parsed_chart)
        }

        # Calculate overall musical coherence score
        coherence_score = np.mean(list(coherence_metrics.values()))

        return {
            "score": coherence_score,
            "metrics": coherence_metrics,
            "details": self.generate_coherence_details(coherence_metrics)
        }

    def analyze_transitions(self, parsed_chart: Dict) -> float:
        """Analyze smoothness of pattern transitions"""

        notes = parsed_chart["notes"]
        if len(notes) < 10:
            return 1.0  # Too short to analyze

        transition_scores = []
        window_size = 8

        for i in range(len(notes) - window_size):
            current_pattern = notes[i:i+window_size//2]
            next_pattern = notes[i+window_size//2:i+window_size]

            # Analyze transition difficulty
            transition_score = self.calculate_transition_difficulty(current_pattern, next_pattern)
            transition_scores.append(transition_score)

        return np.mean(transition_scores) if transition_scores else 1.0

    def analyze_musical_structure(self, parsed_chart: Dict) -> float:
        """Analyze adherence to musical structure"""

        notes = parsed_chart["notes"]
        timing = parsed_chart["timing"]

        # Detect musical sections (intro, verse, chorus, etc.)
        sections = self.detect_musical_sections(notes, timing)

        # Analyze section coherence
        section_scores = []
        for section in sections:
            section_score = self.analyze_section_coherence(section)
            section_scores.append(section_score)

        return np.mean(section_scores) if section_scores else 0.5

class DifficultyValidator:
    """Validate difficulty appropriateness of generated charts"""

    def __init__(self):
        self.difficulty_metrics = {
            "note_density": self.calculate_note_density,
            "pattern_complexity": self.calculate_pattern_complexity,
            "hand_coordination": self.calculate_hand_coordination,
            "special_notes_usage": self.calculate_special_notes_usage,
            "speed_requirements": self.calculate_speed_requirements
        }

        # Difficulty benchmarks for levels 8, 9, 10
        self.benchmarks = {
            8: {"note_density": (3.0, 5.0), "pattern_complexity": (0.3, 0.6)},
            9: {"note_density": (4.0, 7.0), "pattern_complexity": (0.5, 0.8)},
            10: {"note_density": (6.0, 10.0), "pattern_complexity": (0.7, 1.0)}
        }

    def validate(self, tja_content: str, reference_data: Dict = None) -> Dict:
        """Validate difficulty appropriateness"""

        parsed_chart = self.parse_tja_content(tja_content)
        target_difficulty = reference_data.get("difficulty_level", 8) if reference_data else 8

        # Calculate difficulty metrics
        calculated_metrics = {}
        for metric_name, metric_func in self.difficulty_metrics.items():
            calculated_metrics[metric_name] = metric_func(parsed_chart)

        # Compare with benchmarks
        benchmark_scores = {}
        target_benchmarks = self.benchmarks.get(target_difficulty, self.benchmarks[8])

        for metric_name, value in calculated_metrics.items():
            if metric_name in target_benchmarks:
                min_val, max_val = target_benchmarks[metric_name]
                if min_val <= value <= max_val:
                    benchmark_scores[metric_name] = 1.0
                else:
                    # Calculate how far off the value is
                    if value < min_val:
                        benchmark_scores[metric_name] = value / min_val
                    else:
                        benchmark_scores[metric_name] = max_val / value
                    benchmark_scores[metric_name] = max(0.0, benchmark_scores[metric_name])
            else:
                benchmark_scores[metric_name] = 0.5  # Neutral score for unmapped metrics

        overall_difficulty_score = np.mean(list(benchmark_scores.values()))

        return {
            "score": overall_difficulty_score,
            "target_difficulty": target_difficulty,
            "calculated_metrics": calculated_metrics,
            "benchmark_scores": benchmark_scores,
            "difficulty_assessment": self.assess_difficulty_level(calculated_metrics)
        }

    def assess_difficulty_level(self, metrics: Dict) -> Dict:
        """Assess what difficulty level the chart represents"""

        # Calculate similarity to each difficulty level
        level_similarities = {}

        for level, benchmarks in self.benchmarks.items():
            similarity_scores = []

            for metric_name, (min_val, max_val) in benchmarks.items():
                if metric_name in metrics:
                    metric_value = metrics[metric_name]
                    # Calculate similarity to benchmark range
                    if min_val <= metric_value <= max_val:
                        similarity = 1.0
                    else:
                        # Distance from range
                        if metric_value < min_val:
                            distance = min_val - metric_value
                            similarity = max(0.0, 1.0 - distance / min_val)
                        else:
                            distance = metric_value - max_val
                            similarity = max(0.0, 1.0 - distance / max_val)

                    similarity_scores.append(similarity)

            level_similarities[level] = np.mean(similarity_scores) if similarity_scores else 0.0

        # Find best matching difficulty level
        best_match = max(level_similarities.items(), key=lambda x: x[1])

        return {
            "predicted_level": best_match[0],
            "confidence": best_match[1],
            "level_similarities": level_similarities
        }
```

### 7.2 Performance Benchmarking
```python
class PerformanceBenchmark:
    """Comprehensive performance benchmarking for inference pipeline"""

    def __init__(self, inference_system: TJAInferenceSystem):
        self.inference_system = inference_system
        self.benchmark_results = []

    def run_comprehensive_benchmark(self, test_dataset: List[Dict]) -> Dict:
        """Run comprehensive performance benchmark"""

        benchmark_categories = {
            "speed_benchmark": self.run_speed_benchmark,
            "memory_benchmark": self.run_memory_benchmark,
            "accuracy_benchmark": self.run_accuracy_benchmark,
            "scalability_benchmark": self.run_scalability_benchmark,
            "robustness_benchmark": self.run_robustness_benchmark
        }

        results = {}

        for category_name, benchmark_func in benchmark_categories.items():
            logging.info(f"Running {category_name}...")
            try:
                category_results = benchmark_func(test_dataset)
                results[category_name] = category_results
            except Exception as e:
                logging.error(f"Benchmark {category_name} failed: {e}")
                results[category_name] = {"error": str(e)}

        # Generate summary report
        results["summary"] = self.generate_benchmark_summary(results)

        return results

    def run_speed_benchmark(self, test_dataset: List[Dict]) -> Dict:
        """Benchmark inference speed across different scenarios"""

        speed_results = {
            "short_songs": [],  # <2 minutes
            "medium_songs": [], # 2-4 minutes
            "long_songs": []    # >4 minutes
        }

        for test_case in test_dataset:
            # Load audio to determine duration
            audio, sr = librosa.load(test_case["audio_path"], sr=None)
            duration = len(audio) / sr

            # Categorize by duration
            if duration < 120:
                category = "short_songs"
            elif duration < 240:
                category = "medium_songs"
            else:
                category = "long_songs"

            # Measure inference time
            start_time = time.time()
            result = self.inference_system.generate_chart(
                test_case["audio_path"],
                test_case["bpm"],
                test_case["offset"],
                test_case["difficulty"]
            )
            inference_time = time.time() - start_time

            # Calculate realtime factor
            realtime_factor = duration / inference_time

            speed_results[category].append({
                "duration": duration,
                "inference_time": inference_time,
                "realtime_factor": realtime_factor,
                "success": result["success"]
            })

        # Calculate statistics for each category
        for category in speed_results:
            if speed_results[category]:
                times = [r["inference_time"] for r in speed_results[category]]
                factors = [r["realtime_factor"] for r in speed_results[category]]

                speed_results[f"{category}_stats"] = {
                    "mean_inference_time": np.mean(times),
                    "std_inference_time": np.std(times),
                    "mean_realtime_factor": np.mean(factors),
                    "min_realtime_factor": np.min(factors),
                    "success_rate": np.mean([r["success"] for r in speed_results[category]])
                }

        return speed_results

    def run_memory_benchmark(self, test_dataset: List[Dict]) -> Dict:
        """Benchmark memory usage during inference"""

        memory_results = []

        for test_case in test_dataset[:10]:  # Limit to 10 cases for memory testing
            # Clear memory before test
            torch.cuda.empty_cache() if torch.cuda.is_available() else None
            gc.collect()

            # Measure memory before inference
            memory_before = self.get_memory_usage()

            # Run inference
            result = self.inference_system.generate_chart(
                test_case["audio_path"],
                test_case["bpm"],
                test_case["offset"],
                test_case["difficulty"]
            )

            # Measure memory after inference
            memory_after = self.get_memory_usage()

            memory_results.append({
                "memory_before": memory_before,
                "memory_after": memory_after,
                "memory_delta": {
                    "gpu": memory_after["gpu"] - memory_before["gpu"],
                    "ram": memory_after["ram"] - memory_before["ram"]
                },
                "success": result["success"]
            })

        # Calculate memory statistics
        gpu_deltas = [r["memory_delta"]["gpu"] for r in memory_results if r["success"]]
        ram_deltas = [r["memory_delta"]["ram"] for r in memory_results if r["success"]]

        return {
            "individual_results": memory_results,
            "statistics": {
                "mean_gpu_usage": np.mean(gpu_deltas) if gpu_deltas else 0,
                "max_gpu_usage": np.max(gpu_deltas) if gpu_deltas else 0,
                "mean_ram_usage": np.mean(ram_deltas) if ram_deltas else 0,
                "max_ram_usage": np.max(ram_deltas) if ram_deltas else 0
            }
        }

    def get_memory_usage(self) -> Dict:
        """Get current memory usage"""
        memory_info = {"gpu": 0, "ram": 0}

        # GPU memory
        if torch.cuda.is_available():
            memory_info["gpu"] = torch.cuda.memory_allocated() / (1024**3)  # GB

        # RAM usage
        import psutil
        memory_info["ram"] = psutil.virtual_memory().used / (1024**3)  # GB

        return memory_info
```

### 7.3 Human Evaluation Framework
```python
class HumanEvaluationFramework:
    """Framework for human evaluation of generated TJA charts"""

    def __init__(self):
        self.evaluation_criteria = {
            "playability": "How playable is the chart for the target difficulty?",
            "musical_fit": "How well does the chart fit the music?",
            "pattern_quality": "How good are the note patterns and transitions?",
            "difficulty_accuracy": "How accurate is the difficulty level?",
            "overall_quality": "Overall quality compared to human-made charts"
        }

        self.rating_scale = {
            1: "Very Poor",
            2: "Poor",
            3: "Fair",
            4: "Good",
            5: "Excellent"
        }

    def create_evaluation_package(self, generated_charts: List[Dict],
                                reference_charts: List[Dict] = None) -> Dict:
        """Create evaluation package for human evaluators"""

        evaluation_package = {
            "instructions": self.generate_evaluation_instructions(),
            "evaluation_criteria": self.evaluation_criteria,
            "rating_scale": self.rating_scale,
            "charts_to_evaluate": []
        }

        # Prepare charts for evaluation
        for i, chart_data in enumerate(generated_charts):
            chart_package = {
                "chart_id": f"generated_{i:03d}",
                "audio_file": chart_data["audio_path"],
                "tja_content": chart_data["generated_tja"],
                "metadata": {
                    "bpm": chart_data["bpm"],
                    "difficulty": chart_data["difficulty"],
                    "duration": chart_data.get("duration", "unknown")
                },
                "evaluation_form": self.create_evaluation_form()
            }

            # Add reference chart if available
            if reference_charts and i < len(reference_charts):
                chart_package["reference_tja"] = reference_charts[i]["tja_content"]

            evaluation_package["charts_to_evaluate"].append(chart_package)

        return evaluation_package

    def create_evaluation_form(self) -> Dict:
        """Create evaluation form template"""

        form = {
            "evaluator_info": {
                "name": "",
                "experience_level": "",  # Beginner, Intermediate, Advanced, Expert
                "evaluation_date": ""
            },
            "ratings": {},
            "comments": {
                "strengths": "",
                "weaknesses": "",
                "suggestions": "",
                "additional_notes": ""
            }
        }

        # Add rating fields for each criterion
        for criterion in self.evaluation_criteria:
            form["ratings"][criterion] = {
                "score": 0,  # 1-5 scale
                "confidence": 0,  # How confident in this rating (1-5)
                "notes": ""
            }

        return form

    def analyze_evaluation_results(self, completed_evaluations: List[Dict]) -> Dict:
        """Analyze results from human evaluations"""

        if not completed_evaluations:
            return {"error": "No evaluations to analyze"}

        analysis = {
            "summary_statistics": {},
            "criterion_analysis": {},
            "evaluator_agreement": {},
            "qualitative_analysis": {},
            "recommendations": []
        }

        # Calculate summary statistics
        all_scores = []
        criterion_scores = {criterion: [] for criterion in self.evaluation_criteria}

        for evaluation in completed_evaluations:
            for criterion, rating_data in evaluation["ratings"].items():
                score = rating_data["score"]
                all_scores.append(score)
                if criterion in criterion_scores:
                    criterion_scores[criterion].append(score)

        analysis["summary_statistics"] = {
            "total_evaluations": len(completed_evaluations),
            "mean_overall_score": np.mean(all_scores),
            "std_overall_score": np.std(all_scores),
            "score_distribution": np.histogram(all_scores, bins=5, range=(1, 5))[0].tolist()
        }

        # Analyze each criterion
        for criterion, scores in criterion_scores.items():
            if scores:
                analysis["criterion_analysis"][criterion] = {
                    "mean_score": np.mean(scores),
                    "std_score": np.std(scores),
                    "median_score": np.median(scores),
                    "score_range": [np.min(scores), np.max(scores)]
                }

        # Calculate inter-evaluator agreement
        analysis["evaluator_agreement"] = self.calculate_evaluator_agreement(completed_evaluations)

        # Analyze qualitative feedback
        analysis["qualitative_analysis"] = self.analyze_qualitative_feedback(completed_evaluations)

        # Generate recommendations
        analysis["recommendations"] = self.generate_improvement_recommendations(analysis)

        return analysis

    def calculate_evaluator_agreement(self, evaluations: List[Dict]) -> Dict:
        """Calculate agreement between evaluators"""

        if len(evaluations) < 2:
            return {"note": "Need at least 2 evaluators for agreement analysis"}

        # Group evaluations by chart
        chart_evaluations = {}
        for evaluation in evaluations:
            chart_id = evaluation.get("chart_id", "unknown")
            if chart_id not in chart_evaluations:
                chart_evaluations[chart_id] = []
            chart_evaluations[chart_id].append(evaluation)

        # Calculate agreement for charts with multiple evaluations
        agreement_scores = []

        for chart_id, chart_evals in chart_evaluations.items():
            if len(chart_evals) >= 2:
                # Calculate pairwise agreement
                for criterion in self.evaluation_criteria:
                    scores = [eval_data["ratings"][criterion]["score"]
                             for eval_data in chart_evals
                             if criterion in eval_data["ratings"]]

                    if len(scores) >= 2:
                        # Calculate standard deviation as inverse of agreement
                        agreement = 1.0 / (1.0 + np.std(scores))
                        agreement_scores.append(agreement)

        return {
            "mean_agreement": np.mean(agreement_scores) if agreement_scores else 0,
            "agreement_distribution": agreement_scores
        }
```

## 8. Deployment and Production Considerations

### 8.1 Model Deployment Pipeline
```python
class ModelDeployment:
    """Production deployment pipeline for TJA generation model"""

    def __init__(self, model_path: str, deployment_config: Dict):
        self.model_path = model_path
        self.config = deployment_config

        # Initialize deployment components
        self.model_optimizer = ModelOptimizer(deployment_config)
        self.api_server = APIServer(deployment_config)
        self.monitoring = DeploymentMonitoring(deployment_config)

    def prepare_for_deployment(self) -> Dict:
        """Prepare model for production deployment"""

        preparation_steps = {
            "model_optimization": self.optimize_model_for_deployment,
            "api_setup": self.setup_api_endpoints,
            "monitoring_setup": self.setup_monitoring,
            "testing": self.run_deployment_tests,
            "documentation": self.generate_deployment_docs
        }

        results = {}

        for step_name, step_func in preparation_steps.items():
            logging.info(f"Executing deployment step: {step_name}")
            try:
                step_result = step_func()
                results[step_name] = {"status": "success", "result": step_result}
            except Exception as e:
                logging.error(f"Deployment step {step_name} failed: {e}")
                results[step_name] = {"status": "failed", "error": str(e)}

        return results

    def optimize_model_for_deployment(self) -> Dict:
        """Optimize model for production deployment"""

        optimization_results = {}

        # Model quantization
        if self.config.get("enable_quantization", False):
            quantized_model = self.model_optimizer.quantize_model(self.model_path)
            optimization_results["quantization"] = {
                "original_size": self.get_model_size(self.model_path),
                "quantized_size": self.get_model_size(quantized_model),
                "compression_ratio": self.calculate_compression_ratio(self.model_path, quantized_model)
            }

        # ONNX export
        if self.config.get("export_onnx", False):
            onnx_path = self.model_optimizer.export_to_onnx(self.model_path)
            optimization_results["onnx_export"] = {
                "onnx_path": onnx_path,
                "onnx_size": self.get_model_size(onnx_path)
            }

        # TensorRT optimization
        if self.config.get("enable_tensorrt", False):
            tensorrt_engine = self.model_optimizer.create_tensorrt_engine(self.model_path)
            optimization_results["tensorrt"] = {
                "engine_path": tensorrt_engine,
                "optimization_success": True
            }

        return optimization_results

    def setup_api_endpoints(self) -> Dict:
        """Setup API endpoints for model serving"""

        api_config = {
            "endpoints": {
                "/generate": {
                    "method": "POST",
                    "description": "Generate TJA chart from audio",
                    "parameters": {
                        "audio_file": "Audio file (OGG format)",
                        "bpm": "Beats per minute (float)",
                        "offset": "Chart offset in seconds (float)",
                        "difficulty": "Target difficulty level (8, 9, or 10)",
                        "course_type": "Course type (oni or edit)"
                    }
                },
                "/health": {
                    "method": "GET",
                    "description": "Health check endpoint"
                },
                "/metrics": {
                    "method": "GET",
                    "description": "Performance metrics endpoint"
                }
            },
            "rate_limiting": {
                "requests_per_minute": 60,
                "concurrent_requests": 5
            },
            "authentication": self.config.get("require_auth", False)
        }

        return api_config

class APIServer:
    """FastAPI server for TJA generation service"""

    def __init__(self, config: Dict):
        self.config = config
        self.app = FastAPI(title="TJA Generation API", version="1.0.0")
        self.inference_system = None

        # Setup routes
        self.setup_routes()

        # Setup middleware
        self.setup_middleware()

    def setup_routes(self):
        """Setup API routes"""

        @self.app.post("/generate")
        async def generate_chart(
            audio_file: UploadFile = File(...),
            bpm: float = Form(...),
            offset: float = Form(...),
            difficulty: int = Form(...),
            course_type: str = Form(default="oni")
        ):
            """Generate TJA chart from uploaded audio"""

            try:
                # Validate inputs
                if difficulty not in [8, 9, 10]:
                    raise HTTPException(status_code=400, detail="Difficulty must be 8, 9, or 10")

                if course_type not in ["oni", "edit"]:
                    raise HTTPException(status_code=400, detail="Course type must be 'oni' or 'edit'")

                # Save uploaded file temporarily
                temp_audio_path = f"/tmp/{audio_file.filename}"
                with open(temp_audio_path, "wb") as f:
                    f.write(await audio_file.read())

                # Generate chart
                result = self.inference_system.generate_chart(
                    temp_audio_path, bpm, offset, difficulty, course_type
                )

                # Cleanup temporary file
                os.remove(temp_audio_path)

                if result["success"]:
                    return {
                        "success": True,
                        "tja_content": result["tja_chart"],
                        "validation_results": result["validation_results"],
                        "performance_metrics": result["performance_metrics"]
                    }
                else:
                    raise HTTPException(status_code=500, detail=result["error"])

            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))

        @self.app.get("/health")
        async def health_check():
            """Health check endpoint"""
            return {
                "status": "healthy",
                "timestamp": datetime.now().isoformat(),
                "model_loaded": self.inference_system is not None
            }

        @self.app.get("/metrics")
        async def get_metrics():
            """Get performance metrics"""
            if hasattr(self.inference_system, 'performance_monitor'):
                return self.inference_system.performance_monitor.get_metrics()
            else:
                return {"error": "Performance monitoring not available"}
```

---

**Phase 6 Completion Criteria:**
- [ ] Complete end-to-end inference pipeline with chunked processing
- [ ] Optimized performance with <5s generation time and <4GB memory usage
- [ ] Comprehensive validation framework with format, musical, difficulty, and timing validation
- [ ] Quality assurance with >80% human similarity and automated quality scoring
- [ ] Robust error handling and edge case management for production use
- [ ] Production-ready deployment package with API endpoints and monitoring
- [ ] Human evaluation framework with structured assessment criteria
- [ ] Performance benchmarking across speed, memory, accuracy, and scalability
- [ ] Model optimization with quantization, ONNX export, and TensorRT support

## 8. Success Criteria and Performance Targets

### 8.1 Hardware Resource Efficiency Requirements (Mandatory)
```python
PHASE_6_RESOURCE_EFFICIENCY_TARGETS = {
    "gpu_inference_utilization": {
        "sustained_gpu_utilization": 0.70,        # 70% sustained GPU utilization during inference
        "vram_efficiency": 0.95,                  # 95% VRAM efficiency
        "compute_unit_occupancy": 0.75,           # 75% compute unit occupancy
        "tensor_core_utilization": 0.65,         # 65% tensor core utilization
        "gpu_memory_bandwidth_utilization": 0.80, # 80% GPU memory bandwidth usage
        "max_gpu_memory_gb": 4.0                 # Never exceed 4GB GPU memory
    },
    "cpu_memory_efficiency": {
        "memory_utilization": 0.85,               # 85% memory utilization
        "max_ram_usage_gb": 16,                   # Never exceed 16GB RAM
        "cache_hit_ratio": 0.95,                  # 95% cache hit ratio
        "cache_utilization_gb": 16,               # Use 16GB for caching
        "memory_bandwidth_utilization": 0.85,     # 85% memory bandwidth usage
        "buffer_efficiency": 0.90                 # 90% buffer efficiency
    },
    "inference_pipeline_efficiency": {
        "pipeline_utilization": 0.88,             # 88% pipeline utilization
        "preprocessing_efficiency": 0.90,         # 90% preprocessing efficiency
        "model_inference_efficiency": 0.85,       # 85% model inference efficiency
        "postprocessing_efficiency": 0.92,        # 92% postprocessing efficiency
        "end_to_end_efficiency": 0.82             # 82% end-to-end efficiency
    },
    "resource_sustainability": {
        "sustained_resource_efficiency": 0.85,    # 85% sustained resource efficiency
        "peak_memory_usage_ratio": 0.75,          # <75% peak memory usage
        "thermal_efficiency": 0.90,               # 90% thermal efficiency
        "power_efficiency": 0.85                  # 85% power efficiency
    },
    "quality_metrics": {
        "chart_quality_score": 0.80,              # 80% chart quality
        "human_similarity": 0.75,                 # 75% human similarity
        "playability_score": 0.85,                # 85% playability
        "musical_fit_score": 0.80                 # 80% musical fit
    },
    "production_readiness": {
        "api_uptime": 0.95,                       # 95% API uptime
        "error_handling_coverage": 0.90,          # 90% error handling
        "scalability_efficiency": 0.85            # 85% scalability efficiency
    }
}
```

### 8.2 Final System Validation
**Complete System Requirements:**
- End-to-end TJA generation from raw audio input
- Production-ready inference pipeline with monitoring
- Comprehensive validation against human-created charts
- Hardware-optimized performance for RTX 3070 system

**Estimated Timeline:** 2-3 weeks
**Resource Requirements:** GPU-optimized with sustained resource efficiency, comprehensive validation, human evaluation
**Critical Dependencies:** All previous phases completed, resource efficiency implementation, sustained utilization monitoring

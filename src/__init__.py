"""
TJA Generator - Phase 1: Data Analysis and Preprocessing
A comprehensive TJA file parser and analysis system optimized for RTX 3070 hardware.
"""

__version__ = "1.0.0"
__author__ = "TJA Generator Project"
__description__ = "TJA rhythm chart generation system with deep learning"

# Hardware specifications (verified)
HARDWARE_SPECS = {
    "gpu": {
        "model": "NVIDIA GeForce RTX 3070",
        "vram_gb": 8.0,
        "cuda_version": "12.1",
        "pytorch_version": "2.5.1+cu121",
        "memory_constraint": "6.8GB usable (safety margin)"
    },
    "cpu": {
        "physical_cores": 8,
        "logical_cores": 16,
        "recommended_workers": 12,  # Reserve 4 cores for system
        "optimization_target": "parallel_processing"
    },
    "memory": {
        "total_ram_gb": 31.8,
        "available_ram_gb": 28.0,
        "recommended_cache_gb": 16,
        "processing_buffer_gb": 8
    },
    "software": {
        "python_version": "3.12.10",
        "os": "Windows",
        "pytorch_cuda_available": True
    }
}

# Phase 1 resource targets
PHASE_1_RESOURCE_TARGETS = {
    "cpu_utilization": {
        "target_cpu_utilization": 0.75,   # 75% sustained CPU utilization
        "parallel_workers": 12,           # Use 12 of 16 logical cores
        "reserved_cores": 4,              # Reserve 4 cores for system
        "core_efficiency": 0.85,          # 85% per-core efficiency
        "context_switch_ratio": 0.05      # <5% time in context switching
    },
    "memory_efficiency": {
        "memory_utilization": 0.85,       # 85% memory efficiency
        "max_ram_usage_gb": 20,           # Never exceed 20GB of 32GB
        "optimal_ram_usage_gb": 16,       # Target 16GB for efficiency
        "memory_per_worker_gb": 1.5,      # 1.5GB per processing worker
        "cache_hit_ratio": 0.90,          # 90% cache hit ratio
        "memory_bandwidth_utilization": 0.80  # 80% memory bandwidth usage
    },
    "cache_optimization": {
        "cache_utilization_gb": 12,       # 12GB for data caching
        "cache_hit_ratio": 0.90,          # 90% cache hit ratio
        "cache_efficiency": 0.88,         # 88% cache efficiency
        "cache_miss_penalty": 0.05        # <5% performance loss from misses
    },
    "storage_efficiency": {
        "io_utilization": 0.75,           # 75% I/O bandwidth utilization
        "compression_ratio": 0.3,         # 30% of original size
        "concurrent_reads": 4,            # 4 concurrent file reads
        "io_wait_ratio": 0.10,            # <10% time waiting for I/O
        "write_buffer_efficiency": 0.85   # 85% write buffer efficiency
    }
}

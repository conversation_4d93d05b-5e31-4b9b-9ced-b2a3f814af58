"""
Metadata Separator

Ensures strict separation between reference metadata and training notation data.
Prevents contamination of training data with non-musical information.
"""

from typing import Dict, List, Any
from dataclasses import dataclass
import logging


@dataclass
class SeparationResult:
    """Result of metadata/notation separation"""
    reference_metadata: Dict[str, Any]
    notation_metadata: Dict[str, Any]
    notation_data: Dict[str, Any]
    separation_quality_score: float
    contamination_detected: bool
    warnings: List[str]


class MetadataSeparator:
    """Separates TJA data into reference metadata and training notation data"""
    
    # Fields that should NEVER appear in training data
    REFERENCE_ONLY_FIELDS = {
        'title', 'titleen', 'titleja', 'subtitle', 'subtitleen', 'subtitleja',
        'genre', 'maker', 'wave', 'demostart', 'songvol', 'sevol', 'side',
        'bgimage', 'bgmovie', 'taikowebskin', 'lyrics'
    }
    
    # Fields that are essential for training (musical structure)
    NOTATION_ESSENTIAL_FIELDS = {
        'bpm', 'offset', 'notes', 'commands', 'timing_structure', 'measures',
        'pattern_features', 'rhythmic_patterns'
    }
    
    # Course-level reference fields (NOT for training)
    COURSE_REFERENCE_FIELDS = {
        'level', 'scoreinit', 'scorediff'  # Display/scoring only
    }
    
    # Course-level notation fields (FOR training)
    COURSE_NOTATION_FIELDS = {
        'balloon', 'note_sequences', 'timing_commands', 'measure_structure',
        'pattern_features', 'branch_data'
    }
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def separate_tja_data(self, parser_result: Dict) -> SeparationResult:
        """
        Separate TJA parser result into reference and notation data
        
        Args:
            parser_result: Result from CustomTJAParser
            
        Returns:
            SeparationResult with separated data and quality metrics
        """
        warnings = []
        contamination_detected = False
        
        # Extract reference metadata (NOT for training)
        reference_metadata = self._extract_reference_metadata(parser_result)
        
        # Extract notation metadata (FOR training - structure only)
        notation_metadata = self._extract_notation_metadata(parser_result)
        
        # Extract and clean notation data
        notation_data = self._extract_notation_data(parser_result, warnings)
        
        # Validate separation quality
        quality_score = self._calculate_separation_quality(
            reference_metadata, notation_metadata, notation_data
        )
        
        # Check for contamination
        contamination_detected = self._detect_contamination(notation_data, warnings)
        
        return SeparationResult(
            reference_metadata=reference_metadata,
            notation_metadata=notation_metadata,
            notation_data=notation_data,
            separation_quality_score=quality_score,
            contamination_detected=contamination_detected,
            warnings=warnings
        )
    
    def _extract_reference_metadata(self, parser_result: Dict) -> Dict[str, Any]:
        """Extract reference metadata (display/catalog only)"""
        reference_data = {}
        
        # Get parser metadata
        if 'reference_metadata' in parser_result:
            reference_data.update(parser_result['reference_metadata'])
        
        # Ensure all reference fields are present with defaults
        reference_defaults = {
            'title': 'Unknown',
            'titleen': '',
            'titleja': '',
            'subtitle': '',
            'subtitleen': '',
            'subtitleja': '',
            'genre': '',
            'maker': '',
            'wave': '',
            'demostart': 0.0,
            'songvol': 100,
            'sevol': 100,
            'side': '',
            'bgimage': '',
            'bgmovie': '',
            'taikowebskin': ''
        }
        
        for field, default_value in reference_defaults.items():
            if field not in reference_data:
                reference_data[field] = default_value
        
        return reference_data
    
    def _extract_notation_metadata(self, parser_result: Dict) -> Dict[str, Any]:
        """Extract notation metadata (timing/structure for training)"""
        notation_metadata = {}
        
        # Get parser notation metadata
        if 'notation_metadata' in parser_result:
            notation_metadata.update(parser_result['notation_metadata'])
        
        # Ensure essential notation metadata is present
        notation_defaults = {
            'base_bpm': 120.0,
            'offset': 0.0,
            'encoding': 'unknown'
        }
        
        for field, default_value in notation_defaults.items():
            if field not in notation_metadata:
                notation_metadata[field] = default_value
        
        return notation_metadata
    
    def _extract_notation_data(self, parser_result: Dict, warnings: List[str]) -> Dict[str, Any]:
        """Extract and clean notation data for training"""
        notation_data = {}
        
        # Process each difficulty level
        if 'notation_data' in parser_result:
            for difficulty, data in parser_result['notation_data'].items():
                clean_difficulty_data = self._clean_difficulty_data(data, warnings)
                notation_data[difficulty] = clean_difficulty_data
        
        return notation_data
    
    def _clean_difficulty_data(self, difficulty_data: Dict, warnings: List[str]) -> Dict[str, Any]:
        """Clean difficulty-specific data for training"""
        cleaned_data = {
            'reference': {},  # Course reference data (level, scoring)
            'notation': {}    # Pure notation data for training
        }
        
        # Separate course reference data
        for field in self.COURSE_REFERENCE_FIELDS:
            if field in difficulty_data:
                cleaned_data['reference'][field] = difficulty_data[field]
        
        # Extract pure notation data
        if 'notation' in difficulty_data:
            notation = difficulty_data['notation']
            
            # Clean note sequences
            if 'sequences' in notation:
                cleaned_data['notation']['note_sequences'] = self._clean_note_sequences(
                    notation['sequences'], warnings
                )
            
            # Clean timing commands
            if 'timing_commands' in notation:
                cleaned_data['notation']['timing_commands'] = self._clean_timing_commands(
                    notation['timing_commands'], warnings
                )
            
            # Extract pattern features
            if 'pattern_features' in notation:
                cleaned_data['notation']['pattern_features'] = notation['pattern_features']
            
            # Extract measure structure
            if 'structure' in notation:
                cleaned_data['notation']['measure_structure'] = notation['structure']
        
        return cleaned_data
    
    def _clean_note_sequences(self, sequences: List[Dict], warnings: List[str]) -> List[Dict]:
        """Clean note sequences for training"""
        cleaned_sequences = []
        
        for note in sequences:
            # Validate note has required fields
            if not all(field in note for field in ['type', 'position', 'measure', 'timing_ms']):
                warnings.append(f"Note missing required fields: {note}")
                continue
            
            # Normalize note type
            note_type = self._normalize_note_type(note['type'])
            
            cleaned_note = {
                'type': note_type,
                'position': round(float(note['position']), 6),
                'measure': int(note['measure']),
                'timing_ms': round(float(note['timing_ms']), 3)
            }
            
            cleaned_sequences.append(cleaned_note)
        
        return cleaned_sequences
    
    def _clean_timing_commands(self, commands: List[Dict], warnings: List[str]) -> List[Dict]:
        """Clean timing commands for training"""
        cleaned_commands = []
        
        # Commands that are relevant for training
        training_relevant_commands = {
            'BPMCHANGE', 'MEASURE', 'SCROLL', 'GOGOSTART', 'GOGOEND',
            'DELAY', 'BRANCHSTART', 'BRANCHEND', 'N', 'E', 'M'
        }
        
        for cmd in commands:
            if cmd.get('type') not in training_relevant_commands:
                continue
            
            cleaned_cmd = {
                'type': cmd['type'],
                'value': self._normalize_command_value(cmd['type'], cmd.get('value')),
                'timing_ms': round(float(cmd.get('timing_ms', 0)), 3),
                'measure': int(cmd.get('measure', 0))
            }
            
            cleaned_commands.append(cleaned_cmd)
        
        return cleaned_commands
    
    def _normalize_note_type(self, note_type: Any) -> str:
        """Normalize note types to consistent string format"""
        note_mapping = {
            0: "blank", 1: "don", 2: "ka", 3: "don_big", 4: "ka_big",
            5: "drumroll", 6: "drumroll_big", 7: "balloon", 8: "end_roll",
            9: "kusudama", "A": "don_both", "B": "ka_both", "F": "adlib"
        }
        return note_mapping.get(note_type, str(note_type))
    
    def _normalize_command_value(self, cmd_type: str, value: Any) -> Any:
        """Normalize command values for consistency"""
        if cmd_type in ["BPMCHANGE", "SCROLL", "DELAY"]:
            return round(float(value), 3) if value is not None else 0.0
        elif cmd_type == "MEASURE":
            return str(value) if value else "4/4"
        return value
    
    def _calculate_separation_quality(self, reference_metadata: Dict, 
                                    notation_metadata: Dict, notation_data: Dict) -> float:
        """Calculate quality score for separation (0.0 to 1.0)"""
        score_components = []
        
        # Check reference metadata completeness
        required_reference_fields = ['title', 'genre', 'wave']
        reference_completeness = sum(
            1 for field in required_reference_fields 
            if reference_metadata.get(field)
        ) / len(required_reference_fields)
        score_components.append(reference_completeness * 0.3)
        
        # Check notation metadata completeness
        required_notation_fields = ['base_bpm', 'offset']
        notation_completeness = sum(
            1 for field in required_notation_fields 
            if notation_metadata.get(field) is not None
        ) / len(required_notation_fields)
        score_components.append(notation_completeness * 0.3)
        
        # Check notation data quality
        if notation_data:
            notation_quality = sum(
                1 for diff_data in notation_data.values()
                if diff_data.get('notation', {}).get('note_sequences')
            ) / len(notation_data)
            score_components.append(notation_quality * 0.4)
        else:
            score_components.append(0.0)
        
        return sum(score_components)
    
    def _detect_contamination(self, notation_data: Dict, warnings: List[str]) -> bool:
        """Detect if reference metadata contaminated training data"""
        contamination_found = False
        
        for difficulty, data in notation_data.items():
            notation = data.get('notation', {})
            
            # Check for reference field contamination
            for field in self.REFERENCE_ONLY_FIELDS:
                if field in notation:
                    warnings.append(f"Reference field '{field}' found in notation data for {difficulty}")
                    contamination_found = True
        
        return contamination_found

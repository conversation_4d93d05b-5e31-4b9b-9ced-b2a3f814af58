"""
Feature Validator

Validates extracted audio features for quality, consistency, and training suitability.
"""

import torch
import numpy as np
import logging
from typing import Dict, List, Tuple, Any, Optional
from dataclasses import dataclass


@dataclass
class ValidationResult:
    """Feature validation result"""
    valid: bool
    quality_score: float
    warnings: List[str]
    errors: List[str]
    metrics: Dict[str, Any]


@dataclass
class FeatureQualityMetrics:
    """Comprehensive feature quality metrics"""
    # Dimension validation
    correct_dimensions: bool
    time_frames: int
    feature_dims: int
    
    # Numerical validation
    no_nan_values: bool
    no_inf_values: bool
    finite_values: bool
    
    # Statistical validation
    dynamic_range: float
    mean_energy: float
    std_energy: float
    
    # Spectral validation
    spectral_quality_score: float
    
    # Rhythmic validation
    rhythmic_quality_score: float
    
    # Temporal validation
    temporal_quality_score: float
    
    # Overall quality
    overall_quality_score: float


class FeatureValidator:
    """Validates extracted audio features for training suitability"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Quality thresholds
        self.thresholds = {
            "min_dynamic_range": 10.0,      # Minimum dynamic range in dB
            "max_nan_ratio": 0.01,          # Maximum 1% NaN values
            "min_energy_variance": 0.001,   # Minimum energy variance
            "min_spectral_variance": 0.01,  # Minimum spectral variance
            "min_quality_score": 0.7        # Minimum overall quality score
        }
    
    def validate_feature_tensor(self, features: torch.Tensor, 
                               expected_dims: Tuple[int, int] = None) -> ValidationResult:
        """
        Validate a feature tensor for quality and consistency
        
        Args:
            features: Feature tensor [time, features]
            expected_dims: Expected (time, feature) dimensions
            
        Returns:
            ValidationResult with detailed validation information
        """
        warnings = []
        errors = []
        metrics = {}
        
        try:
            # Basic dimension validation
            if features.dim() != 2:
                errors.append(f"Expected 2D tensor, got {features.dim()}D")
                return ValidationResult(False, 0.0, warnings, errors, metrics)
            
            time_frames, feature_dims = features.shape
            
            # Expected dimension validation
            if expected_dims:
                expected_time, expected_features = expected_dims
                if expected_time and time_frames != expected_time:
                    warnings.append(f"Time dimension mismatch: {time_frames} != {expected_time}")
                if feature_dims != expected_features:
                    errors.append(f"Feature dimension mismatch: {feature_dims} != {expected_features}")
            
            # Numerical validation
            has_nan = torch.isnan(features).any().item()
            has_inf = torch.isinf(features).any().item()
            is_finite = torch.isfinite(features).all().item()
            
            if has_nan:
                nan_ratio = torch.isnan(features).float().mean().item()
                if nan_ratio > self.thresholds["max_nan_ratio"]:
                    errors.append(f"Too many NaN values: {nan_ratio:.3f} > {self.thresholds['max_nan_ratio']}")
                else:
                    warnings.append(f"Some NaN values detected: {nan_ratio:.3f}")
            
            if has_inf:
                errors.append("Infinite values detected in features")
            
            # Statistical validation
            if is_finite and not has_nan:
                feature_min = torch.min(features).item()
                feature_max = torch.max(features).item()
                dynamic_range = feature_max - feature_min
                
                mean_values = torch.mean(features, dim=0)
                std_values = torch.std(features, dim=0)
                
                # Check for sufficient dynamic range
                if dynamic_range < self.thresholds["min_dynamic_range"]:
                    warnings.append(f"Low dynamic range: {dynamic_range:.2f}")
                
                # Check for sufficient variance
                mean_variance = torch.mean(std_values).item()
                if mean_variance < self.thresholds["min_energy_variance"]:
                    warnings.append(f"Low feature variance: {mean_variance:.6f}")
                
                metrics.update({
                    "dynamic_range": dynamic_range,
                    "mean_energy": torch.mean(mean_values).item(),
                    "std_energy": mean_variance,
                    "feature_min": feature_min,
                    "feature_max": feature_max
                })
            else:
                metrics.update({
                    "dynamic_range": 0.0,
                    "mean_energy": 0.0,
                    "std_energy": 0.0,
                    "feature_min": 0.0,
                    "feature_max": 0.0
                })
            
            # Calculate quality score
            quality_score = self._calculate_quality_score(features, metrics, has_nan, has_inf)
            
            # Overall validation
            is_valid = (
                len(errors) == 0 and
                quality_score >= self.thresholds["min_quality_score"]
            )
            
            metrics.update({
                "time_frames": time_frames,
                "feature_dims": feature_dims,
                "has_nan": has_nan,
                "has_inf": has_inf,
                "is_finite": is_finite,
                "quality_score": quality_score
            })
            
            return ValidationResult(
                valid=is_valid,
                quality_score=quality_score,
                warnings=warnings,
                errors=errors,
                metrics=metrics
            )
            
        except Exception as e:
            self.logger.error(f"Error during feature validation: {e}")
            errors.append(f"Validation error: {str(e)}")
            return ValidationResult(False, 0.0, warnings, errors, metrics)
    
    def validate_spectral_features(self, spectral_features: torch.Tensor) -> Dict[str, Any]:
        """Validate spectral features specifically"""
        try:
            validation = {}
            
            # Expected spectral dimensions: 128 mel + 13 mfcc + 12 chroma = 153
            expected_spectral_dims = 153
            
            if spectral_features.shape[1] != expected_spectral_dims:
                validation["dimension_error"] = f"Expected {expected_spectral_dims} spectral dims, got {spectral_features.shape[1]}"
                return validation
            
            # Split into components
            mel_features = spectral_features[:, :128]
            mfcc_features = spectral_features[:, 128:141]
            chroma_features = spectral_features[:, 141:153]
            
            # Validate mel-spectrogram
            mel_dynamic_range = torch.max(mel_features) - torch.min(mel_features)
            validation["mel_dynamic_range"] = mel_dynamic_range.item()
            validation["mel_valid"] = mel_dynamic_range > 20.0  # At least 20dB range
            
            # Validate MFCC
            mfcc_variance = torch.var(mfcc_features, dim=0).mean()
            validation["mfcc_variance"] = mfcc_variance.item()
            validation["mfcc_valid"] = mfcc_variance > 0.1
            
            # Validate chroma
            chroma_sum = torch.sum(chroma_features, dim=1)
            chroma_normalized = torch.all(chroma_sum > 0.1)  # Should have some harmonic content
            validation["chroma_normalized"] = chroma_normalized.item()
            validation["chroma_valid"] = chroma_normalized
            
            # Overall spectral quality
            spectral_quality = (
                float(validation["mel_valid"]) * 0.5 +
                float(validation["mfcc_valid"]) * 0.3 +
                float(validation["chroma_valid"]) * 0.2
            )
            validation["spectral_quality_score"] = spectral_quality
            
            return validation
            
        except Exception as e:
            self.logger.error(f"Error validating spectral features: {e}")
            return {"validation_error": str(e), "spectral_quality_score": 0.0}
    
    def validate_rhythmic_features(self, rhythmic_features: torch.Tensor) -> Dict[str, Any]:
        """Validate rhythmic features specifically"""
        try:
            validation = {}
            
            # Expected rhythmic dimensions: 32
            expected_rhythmic_dims = 32
            
            if rhythmic_features.shape[1] != expected_rhythmic_dims:
                validation["dimension_error"] = f"Expected {expected_rhythmic_dims} rhythmic dims, got {rhythmic_features.shape[1]}"
                return validation
            
            # Validate onset strength (first column)
            onset_strength = rhythmic_features[:, 0]
            onset_peaks = torch.sum(onset_strength > 0.1).item()
            validation["onset_peaks"] = onset_peaks
            validation["onset_density"] = onset_peaks / len(onset_strength)
            validation["onset_valid"] = onset_peaks > 10  # At least 10 onset peaks
            
            # Validate tempo features (columns 4-7)
            tempo_features = rhythmic_features[:, 4:8]
            tempo_variance = torch.var(tempo_features, dim=0).mean()
            validation["tempo_variance"] = tempo_variance.item()
            validation["tempo_valid"] = tempo_variance > 0.001
            
            # Validate beat-synchronous features (columns 8-31)
            beat_sync_features = rhythmic_features[:, 8:32]
            beat_sync_energy = torch.mean(torch.abs(beat_sync_features))
            validation["beat_sync_energy"] = beat_sync_energy.item()
            validation["beat_sync_valid"] = beat_sync_energy > 0.01
            
            # Overall rhythmic quality
            rhythmic_quality = (
                float(validation["onset_valid"]) * 0.4 +
                float(validation["tempo_valid"]) * 0.3 +
                float(validation["beat_sync_valid"]) * 0.3
            )
            validation["rhythmic_quality_score"] = rhythmic_quality
            
            return validation
            
        except Exception as e:
            self.logger.error(f"Error validating rhythmic features: {e}")
            return {"validation_error": str(e), "rhythmic_quality_score": 0.0}
    
    def validate_temporal_features(self, temporal_features: torch.Tensor) -> Dict[str, Any]:
        """Validate temporal features specifically"""
        try:
            validation = {}
            
            # Expected temporal dimensions: 16
            expected_temporal_dims = 16
            
            if temporal_features.shape[1] != expected_temporal_dims:
                validation["dimension_error"] = f"Expected {expected_temporal_dims} temporal dims, got {temporal_features.shape[1]}"
                return validation
            
            # Validate RMS energy (first column)
            rms_energy = temporal_features[:, 0]
            rms_variance = torch.var(rms_energy)
            validation["rms_variance"] = rms_variance.item()
            validation["rms_valid"] = rms_variance > 0.001
            
            # Validate spectral features (columns 1-4)
            spectral_stats = temporal_features[:, 1:5]
            spectral_variance = torch.var(spectral_stats, dim=0).mean()
            validation["spectral_stats_variance"] = spectral_variance.item()
            validation["spectral_stats_valid"] = spectral_variance > 0.01
            
            # Validate spectral contrast (columns 5-10)
            spectral_contrast = temporal_features[:, 5:11]
            contrast_energy = torch.mean(torch.abs(spectral_contrast))
            validation["contrast_energy"] = contrast_energy.item()
            validation["contrast_valid"] = contrast_energy > 0.01
            
            # Overall temporal quality
            temporal_quality = (
                float(validation["rms_valid"]) * 0.4 +
                float(validation["spectral_stats_valid"]) * 0.3 +
                float(validation["contrast_valid"]) * 0.3
            )
            validation["temporal_quality_score"] = temporal_quality
            
            return validation
            
        except Exception as e:
            self.logger.error(f"Error validating temporal features: {e}")
            return {"validation_error": str(e), "temporal_quality_score": 0.0}
    
    def validate_complete_features(self, features: Dict[str, torch.Tensor]) -> ValidationResult:
        """
        Validate complete feature extraction results
        
        Args:
            features: Dictionary containing all extracted features
            
        Returns:
            ValidationResult with comprehensive validation
        """
        warnings = []
        errors = []
        metrics = {}
        
        try:
            # Validate main combined features
            combined_features = features.get("combined_features")
            if combined_features is None:
                errors.append("Missing combined_features")
                return ValidationResult(False, 0.0, warnings, errors, metrics)
            
            # Validate dimensions [T, 201]
            main_validation = self.validate_feature_tensor(combined_features, (None, 201))
            warnings.extend(main_validation.warnings)
            errors.extend(main_validation.errors)
            metrics.update(main_validation.metrics)
            
            # Validate individual feature components
            if "spectral_features" in features:
                spectral_validation = self.validate_spectral_features(features["spectral_features"])
                metrics["spectral_validation"] = spectral_validation
                
                if "validation_error" in spectral_validation:
                    errors.append(f"Spectral validation error: {spectral_validation['validation_error']}")
            
            if "rhythmic_features" in features:
                rhythmic_validation = self.validate_rhythmic_features(features["rhythmic_features"])
                metrics["rhythmic_validation"] = rhythmic_validation
                
                if "validation_error" in rhythmic_validation:
                    errors.append(f"Rhythmic validation error: {rhythmic_validation['validation_error']}")
            
            if "temporal_features" in features:
                temporal_validation = self.validate_temporal_features(features["temporal_features"])
                metrics["temporal_validation"] = temporal_validation
                
                if "validation_error" in temporal_validation:
                    errors.append(f"Temporal validation error: {temporal_validation['validation_error']}")
            
            # Calculate comprehensive quality score
            quality_components = []
            if "spectral_validation" in metrics:
                quality_components.append(metrics["spectral_validation"].get("spectral_quality_score", 0.0))
            if "rhythmic_validation" in metrics:
                quality_components.append(metrics["rhythmic_validation"].get("rhythmic_quality_score", 0.0))
            if "temporal_validation" in metrics:
                quality_components.append(metrics["temporal_validation"].get("temporal_quality_score", 0.0))
            
            if quality_components:
                comprehensive_quality = sum(quality_components) / len(quality_components)
            else:
                comprehensive_quality = main_validation.quality_score
            
            # Overall validation
            is_valid = (
                len(errors) == 0 and
                comprehensive_quality >= self.thresholds["min_quality_score"]
            )
            
            metrics["comprehensive_quality_score"] = comprehensive_quality
            
            return ValidationResult(
                valid=is_valid,
                quality_score=comprehensive_quality,
                warnings=warnings,
                errors=errors,
                metrics=metrics
            )
            
        except Exception as e:
            self.logger.error(f"Error during complete feature validation: {e}")
            errors.append(f"Complete validation error: {str(e)}")
            return ValidationResult(False, 0.0, warnings, errors, metrics)
    
    def _calculate_quality_score(self, features: torch.Tensor, metrics: Dict[str, Any],
                                has_nan: bool, has_inf: bool) -> float:
        """Calculate overall quality score for features"""
        score_components = []
        
        # Numerical validity (40% weight)
        if has_nan or has_inf:
            numerical_score = 0.0
        else:
            numerical_score = 1.0
        score_components.append(numerical_score * 0.4)
        
        # Dynamic range (30% weight)
        dynamic_range = metrics.get("dynamic_range", 0.0)
        range_score = min(1.0, dynamic_range / 50.0)  # Normalize to 50dB max
        score_components.append(range_score * 0.3)
        
        # Variance (30% weight)
        variance = metrics.get("std_energy", 0.0)
        variance_score = min(1.0, variance / 1.0)  # Normalize to 1.0 max
        score_components.append(variance_score * 0.3)
        
        return sum(score_components)

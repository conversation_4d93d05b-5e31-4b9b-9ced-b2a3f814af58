"""
Alignment Validator

Validates temporal alignment between audio features and TJA notation data.
Ensures precise synchronization for training data quality.
"""

import torch
import numpy as np
import logging
from typing import Dict, List, Tuple, Any, Optional
from dataclasses import dataclass


@dataclass
class AlignmentValidationResult:
    """Temporal alignment validation result"""
    valid: bool
    alignment_accuracy: float
    mean_error_ms: float
    max_error_ms: float
    warnings: List[str]
    errors: List[str]
    metrics: Dict[str, Any]


class AlignmentValidator:
    """Validates temporal alignment between features and notation"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Alignment quality thresholds
        self.thresholds = {
            "max_mean_error_ms": 25.0,      # Maximum 25ms mean error
            "max_absolute_error_ms": 50.0,  # Maximum 50ms absolute error
            "min_alignment_accuracy": 0.90, # Minimum 90% accuracy
            "alignment_tolerance_ms": 20.0   # 20ms tolerance for "correct" alignment
        }
    
    def validate_timing_grid(self, timing_grid: Dict[str, torch.Tensor], 
                           audio_duration: float, base_bpm: float) -> AlignmentValidationResult:
        """
        Validate timing grid consistency and accuracy
        
        Args:
            timing_grid: Timing grid from TemporalAligner
            audio_duration: Expected audio duration in seconds
            base_bpm: Expected base BPM
            
        Returns:
            AlignmentValidationResult with validation details
        """
        warnings = []
        errors = []
        metrics = {}
        
        try:
            # Check required components
            required_keys = ["beat_grid", "measure_grid", "time_axis", "bpm_sequence"]
            for key in required_keys:
                if key not in timing_grid:
                    errors.append(f"Missing timing grid component: {key}")
            
            if errors:
                return AlignmentValidationResult(
                    False, 0.0, float('inf'), float('inf'), warnings, errors, metrics
                )
            
            time_axis = timing_grid["time_axis"]
            beat_grid = timing_grid["beat_grid"]
            bpm_sequence = timing_grid["bpm_sequence"]
            
            # Validate time axis
            actual_duration = time_axis[-1].item() if len(time_axis) > 0 else 0.0
            duration_error = abs(actual_duration - audio_duration)
            
            if duration_error > 1.0:  # 1 second tolerance
                warnings.append(f"Duration mismatch: {actual_duration:.2f}s vs {audio_duration:.2f}s")
            
            # Validate BPM consistency
            mean_bpm = torch.mean(bpm_sequence).item()
            bpm_error = abs(mean_bpm - base_bpm)
            
            if bpm_error > 5.0:  # 5 BPM tolerance
                warnings.append(f"BPM mismatch: {mean_bpm:.1f} vs {base_bpm:.1f}")
            
            # Validate beat grid regularity
            beat_positions = torch.nonzero(beat_grid > 0.5).squeeze()
            if len(beat_positions) > 1:
                beat_intervals = torch.diff(beat_positions.float())
                expected_interval = 50.0 * 60.0 / base_bpm  # frames per beat at 50fps
                
                interval_errors = torch.abs(beat_intervals - expected_interval)
                mean_interval_error = torch.mean(interval_errors).item()
                max_interval_error = torch.max(interval_errors).item()
                
                metrics.update({
                    "beat_count": len(beat_positions),
                    "mean_beat_interval": torch.mean(beat_intervals).item(),
                    "expected_beat_interval": expected_interval,
                    "mean_interval_error": mean_interval_error,
                    "max_interval_error": max_interval_error
                })
                
                if mean_interval_error > expected_interval * 0.1:  # 10% tolerance
                    warnings.append(f"Irregular beat intervals: {mean_interval_error:.1f} frames error")
            
            # Calculate alignment accuracy
            alignment_accuracy = self._calculate_timing_accuracy(timing_grid, base_bpm)
            
            # Overall validation
            is_valid = (
                len(errors) == 0 and
                alignment_accuracy >= self.thresholds["min_alignment_accuracy"]
            )
            
            metrics.update({
                "actual_duration": actual_duration,
                "expected_duration": audio_duration,
                "duration_error": duration_error,
                "mean_bpm": mean_bpm,
                "expected_bpm": base_bpm,
                "bpm_error": bpm_error,
                "alignment_accuracy": alignment_accuracy
            })
            
            return AlignmentValidationResult(
                valid=is_valid,
                alignment_accuracy=alignment_accuracy,
                mean_error_ms=duration_error * 1000,  # Convert to ms
                max_error_ms=max(duration_error * 1000, bpm_error * 1000 / base_bpm),
                warnings=warnings,
                errors=errors,
                metrics=metrics
            )
            
        except Exception as e:
            self.logger.error(f"Error validating timing grid: {e}")
            errors.append(f"Timing grid validation error: {str(e)}")
            return AlignmentValidationResult(
                False, 0.0, float('inf'), float('inf'), warnings, errors, metrics
            )
    
    def validate_feature_note_alignment(self, features: torch.Tensor,
                                      note_timings_ms: List[float],
                                      timing_grid: Dict[str, torch.Tensor]) -> AlignmentValidationResult:
        """
        Validate alignment between features and note timings
        
        Args:
            features: Feature tensor [time, features]
            note_timings_ms: List of note timings in milliseconds
            timing_grid: Timing grid for alignment
            
        Returns:
            AlignmentValidationResult with alignment quality metrics
        """
        warnings = []
        errors = []
        metrics = {}
        
        try:
            if len(note_timings_ms) == 0:
                warnings.append("No note timings provided for alignment validation")
                return AlignmentValidationResult(
                    True, 1.0, 0.0, 0.0, warnings, errors, {"note_count": 0}
                )
            
            time_axis = timing_grid["time_axis"]
            frame_rate = 50.0  # 50 FPS
            
            # Calculate alignment errors for each note
            alignment_errors_ms = []
            valid_alignments = 0
            
            for note_time_ms in note_timings_ms:
                note_time_s = note_time_ms / 1000.0
                
                # Find closest frame
                time_diffs = torch.abs(time_axis - note_time_s)
                closest_frame_idx = torch.argmin(time_diffs).item()
                
                # Calculate alignment error
                closest_time_s = time_axis[closest_frame_idx].item()
                error_ms = abs(closest_time_s - note_time_s) * 1000
                alignment_errors_ms.append(error_ms)
                
                # Check if alignment is within tolerance
                if error_ms <= self.thresholds["alignment_tolerance_ms"]:
                    valid_alignments += 1
            
            # Calculate alignment statistics
            mean_error_ms = np.mean(alignment_errors_ms)
            max_error_ms = np.max(alignment_errors_ms)
            alignment_accuracy = valid_alignments / len(note_timings_ms)
            
            # Validate against thresholds
            if mean_error_ms > self.thresholds["max_mean_error_ms"]:
                errors.append(f"Mean alignment error too high: {mean_error_ms:.1f}ms > {self.thresholds['max_mean_error_ms']}ms")
            
            if max_error_ms > self.thresholds["max_absolute_error_ms"]:
                errors.append(f"Maximum alignment error too high: {max_error_ms:.1f}ms > {self.thresholds['max_absolute_error_ms']}ms")
            
            if alignment_accuracy < self.thresholds["min_alignment_accuracy"]:
                errors.append(f"Alignment accuracy too low: {alignment_accuracy:.2f} < {self.thresholds['min_alignment_accuracy']}")
            
            # Additional quality checks
            if mean_error_ms > 15.0:  # Warning threshold
                warnings.append(f"High mean alignment error: {mean_error_ms:.1f}ms")
            
            # Calculate error distribution
            error_std = np.std(alignment_errors_ms)
            error_percentiles = np.percentile(alignment_errors_ms, [50, 75, 90, 95])
            
            metrics.update({
                "note_count": len(note_timings_ms),
                "valid_alignments": valid_alignments,
                "alignment_accuracy": alignment_accuracy,
                "mean_error_ms": mean_error_ms,
                "max_error_ms": max_error_ms,
                "error_std_ms": error_std,
                "error_median_ms": error_percentiles[0],
                "error_75th_percentile_ms": error_percentiles[1],
                "error_90th_percentile_ms": error_percentiles[2],
                "error_95th_percentile_ms": error_percentiles[3],
                "alignment_errors_ms": alignment_errors_ms
            })
            
            # Overall validation
            is_valid = (
                len(errors) == 0 and
                alignment_accuracy >= self.thresholds["min_alignment_accuracy"]
            )
            
            return AlignmentValidationResult(
                valid=is_valid,
                alignment_accuracy=alignment_accuracy,
                mean_error_ms=mean_error_ms,
                max_error_ms=max_error_ms,
                warnings=warnings,
                errors=errors,
                metrics=metrics
            )
            
        except Exception as e:
            self.logger.error(f"Error validating feature-note alignment: {e}")
            errors.append(f"Feature-note alignment validation error: {str(e)}")
            return AlignmentValidationResult(
                False, 0.0, float('inf'), float('inf'), warnings, errors, metrics
            )
    
    def validate_bpm_consistency(self, timing_grid: Dict[str, torch.Tensor],
                                timing_commands: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Validate BPM consistency with timing commands
        
        Args:
            timing_grid: Timing grid from TemporalAligner
            timing_commands: List of timing commands from TJA
            
        Returns:
            Dictionary with BPM consistency validation results
        """
        try:
            validation = {}
            
            bpm_sequence = timing_grid["bpm_sequence"]
            time_axis = timing_grid["time_axis"]
            
            # Extract BPM changes from timing commands
            bpm_changes = []
            for cmd in timing_commands:
                if cmd.get("type") == "BPMCHANGE":
                    bmp_changes.append({
                        "time_ms": cmd.get("timing_ms", 0.0),
                        "new_bpm": cmd.get("value", 120.0)
                    })
            
            validation["bpm_change_count"] = len(bpm_changes)
            
            if len(bpm_changes) == 0:
                # No BPM changes - validate constant BPM
                bpm_variance = torch.var(bpm_sequence).item()
                validation["bpm_variance"] = bpm_variance
                validation["constant_bpm"] = bpm_variance < 1.0  # Less than 1 BPM variance
                validation["bpm_consistency_score"] = 1.0 if bpm_variance < 1.0 else 0.5
            else:
                # Validate BPM changes are applied correctly
                consistency_scores = []
                
                for change in bpm_changes:
                    change_time_s = change["time_ms"] / 1000.0
                    expected_bpm = change["new_bpm"]
                    
                    # Find the time index for this change
                    change_idx = torch.argmin(torch.abs(time_axis - change_time_s)).item()
                    
                    # Check BPM after the change
                    if change_idx < len(bpm_sequence) - 10:  # Check 10 frames after
                        actual_bpm = torch.mean(bpm_sequence[change_idx:change_idx+10]).item()
                        bpm_error = abs(actual_bpm - expected_bpm)
                        
                        consistency_score = max(0.0, 1.0 - bpm_error / 10.0)  # 10 BPM tolerance
                        consistency_scores.append(consistency_score)
                
                if consistency_scores:
                    validation["bpm_consistency_score"] = np.mean(consistency_scores)
                else:
                    validation["bpm_consistency_score"] = 0.0
            
            return validation
            
        except Exception as e:
            self.logger.error(f"Error validating BPM consistency: {e}")
            return {
                "validation_error": str(e),
                "bpm_consistency_score": 0.0
            }
    
    def _calculate_timing_accuracy(self, timing_grid: Dict[str, torch.Tensor], 
                                 base_bpm: float) -> float:
        """Calculate overall timing accuracy score"""
        try:
            beat_grid = timing_grid["beat_grid"]
            time_axis = timing_grid["time_axis"]
            
            # Find beat positions
            beat_positions = torch.nonzero(beat_grid > 0.5).squeeze()
            
            if len(beat_positions) < 2:
                return 0.5  # Insufficient beats for accuracy calculation
            
            # Calculate expected beat interval
            expected_interval_s = 60.0 / base_bpm
            expected_interval_frames = expected_interval_s * 50.0  # 50 FPS
            
            # Calculate actual intervals
            actual_intervals = torch.diff(beat_positions.float())
            
            # Calculate accuracy based on interval consistency
            interval_errors = torch.abs(actual_intervals - expected_interval_frames)
            relative_errors = interval_errors / expected_interval_frames
            
            # Accuracy is percentage of intervals within 10% tolerance
            accurate_intervals = torch.sum(relative_errors < 0.1).item()
            accuracy = accurate_intervals / len(actual_intervals)
            
            return accuracy
            
        except Exception as e:
            self.logger.error(f"Error calculating timing accuracy: {e}")
            return 0.0

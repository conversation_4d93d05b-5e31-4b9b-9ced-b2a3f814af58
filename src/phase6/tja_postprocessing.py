"""
TJA Post-Processing Pipeline

Advanced post-processing for converting model outputs to valid TJA format
with pattern coherence, difficulty validation, and format compliance.
"""

import numpy as np
import torch
import re
import logging
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from pathlib import Path


@dataclass
class TJANote:
    """Represents a single TJA note"""
    time: float
    note_type: int  # 0=empty, 1=don, 2=ka, 3=big_don, 4=big_ka, 5=drumroll_start, 6=big_drumroll_start, 7=balloon, 8=end, 9=big_balloon
    position: float  # Beat position
    measure: int
    confidence: float = 1.0


@dataclass
class TJAChart:
    """Complete TJA chart representation"""
    title: str
    artist: str
    bpm: float
    offset: float
    difficulty_level: int
    course_type: str
    notes: List[TJANote]
    metadata: Dict[str, Any]
    measures: List[str]  # TJA measure strings


class TJAFormatValidator:
    """
    TJA format validation and compliance checking
    
    Ensures generated charts comply with TJA specification
    and are playable in Taiko no Tatsujin simulators.
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Valid note types
        self.valid_note_types = {0, 1, 2, 3, 4, 5, 6, 7, 8, 9}
        
        # TJA format patterns
        self.header_pattern = re.compile(r'^[A-Z]+:.*$')
        self.measure_pattern = re.compile(r'^[0-9,]+$')
    
    def validate_chart(self, tja_chart: TJAChart) -> Dict[str, Any]:
        """Validate complete TJA chart"""
        validation_results = {
            "valid": True,
            "errors": [],
            "warnings": [],
            "format_score": 1.0
        }
        
        try:
            # Validate header information
            self._validate_header(tja_chart, validation_results)
            
            # Validate note sequence
            self._validate_notes(tja_chart, validation_results)
            
            # Validate measures
            self._validate_measures(tja_chart, validation_results)
            
            # Validate timing consistency
            self._validate_timing(tja_chart, validation_results)
            
            # Calculate format score
            validation_results["format_score"] = self._calculate_format_score(validation_results)
            
            if validation_results["errors"]:
                validation_results["valid"] = False
            
            self.logger.debug(f"Format validation completed: {validation_results['format_score']:.3f}")
            
        except Exception as e:
            validation_results["valid"] = False
            validation_results["errors"].append(f"Validation failed: {str(e)}")
            validation_results["format_score"] = 0.0
        
        return validation_results
    
    def _validate_header(self, chart: TJAChart, results: Dict):
        """Validate TJA header information"""
        if not chart.title:
            results["warnings"].append("Missing title")
        
        if not chart.artist:
            results["warnings"].append("Missing artist")
        
        if chart.bpm <= 0:
            results["errors"].append("Invalid BPM")
        
        if chart.difficulty_level not in [8, 9, 10]:
            results["errors"].append("Invalid difficulty level")
        
        if chart.course_type not in ["oni", "edit"]:
            results["errors"].append("Invalid course type")
    
    def _validate_notes(self, chart: TJAChart, results: Dict):
        """Validate note sequence"""
        for i, note in enumerate(chart.notes):
            if note.note_type not in self.valid_note_types:
                results["errors"].append(f"Invalid note type at position {i}: {note.note_type}")
            
            if note.time < 0:
                results["errors"].append(f"Negative time at position {i}")
            
            if note.confidence < 0 or note.confidence > 1:
                results["warnings"].append(f"Invalid confidence at position {i}")
    
    def _validate_measures(self, chart: TJAChart, results: Dict):
        """Validate measure formatting"""
        for i, measure in enumerate(chart.measures):
            if not self.measure_pattern.match(measure.replace(',', '')):
                results["errors"].append(f"Invalid measure format at measure {i}")
    
    def _validate_timing(self, chart: TJAChart, results: Dict):
        """Validate timing consistency"""
        if len(chart.notes) < 2:
            return
        
        # Check for overlapping notes
        for i in range(len(chart.notes) - 1):
            if chart.notes[i].time >= chart.notes[i + 1].time:
                results["warnings"].append(f"Non-increasing time at position {i}")
        
        # Check minimum note gaps
        min_gap = 0.05  # 50ms minimum gap
        for i in range(len(chart.notes) - 1):
            gap = chart.notes[i + 1].time - chart.notes[i].time
            if gap < min_gap and chart.notes[i].note_type != 0:
                results["warnings"].append(f"Very short note gap at position {i}: {gap:.3f}s")
    
    def _calculate_format_score(self, results: Dict) -> float:
        """Calculate format compliance score"""
        error_penalty = len(results["errors"]) * 0.2
        warning_penalty = len(results["warnings"]) * 0.05
        
        score = max(0.0, 1.0 - error_penalty - warning_penalty)
        return score


class TJAPostProcessor:
    """
    Complete TJA post-processing pipeline
    
    Converts model outputs to valid TJA format with pattern coherence,
    difficulty validation, and format compliance.
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.note_threshold = config.get("note_threshold", 0.5)
        self.density_smoothing = config.get("density_smoothing", True)
        self.pattern_coherence_check = config.get("pattern_coherence_check", True)
        self.quantization_enabled = config.get("quantization_enabled", True)
        self.minimum_note_gap = config.get("minimum_note_gap", 0.05)
        self.max_notes_per_measure = config.get("max_notes_per_measure", 32)
        
        self.format_validator = TJAFormatValidator()
        self.logger = logging.getLogger(__name__)
        
        self.logger.info("TJA post-processor initialized")
    
    def generate_tja(self, 
                    model_outputs: Dict[str, Any],
                    timing_grid: Dict[str, Any],
                    bpm: float,
                    offset: float,
                    difficulty_level: int,
                    course_type: str,
                    metadata: Optional[Dict[str, Any]] = None) -> TJAChart:
        """
        Generate complete TJA chart from model outputs
        
        Args:
            model_outputs: Raw model predictions
            timing_grid: Timing information
            bpm: Beats per minute
            offset: Chart offset
            difficulty_level: Target difficulty (8, 9, 10)
            course_type: Course type ("oni" or "edit")
            metadata: Additional metadata
            
        Returns:
            Complete TJAChart object
        """
        try:
            # Extract note predictions
            note_predictions = self._extract_note_predictions(model_outputs)
            
            # Apply thresholding and filtering
            filtered_notes = self._filter_predictions(note_predictions, timing_grid)
            
            # Apply density smoothing
            if self.density_smoothing:
                filtered_notes = self._apply_density_smoothing(filtered_notes, difficulty_level)
            
            # Apply pattern coherence
            if self.pattern_coherence_check:
                filtered_notes = self._ensure_pattern_coherence(filtered_notes)
            
            # Quantize to beat grid
            if self.quantization_enabled:
                filtered_notes = self._quantize_notes(filtered_notes, timing_grid)
            
            # Create TJA notes
            tja_notes = self._create_tja_notes(filtered_notes, timing_grid)
            
            # Generate measures
            measures = self._generate_measures(tja_notes, timing_grid, bpm)
            
            # Create chart object
            chart = TJAChart(
                title=metadata.get("title", "Generated Chart") if metadata else "Generated Chart",
                artist=metadata.get("artist", "AI Generated") if metadata else "AI Generated",
                bpm=bpm,
                offset=offset,
                difficulty_level=difficulty_level,
                course_type=course_type,
                notes=tja_notes,
                metadata=metadata or {},
                measures=measures
            )
            
            self.logger.info(f"Generated TJA chart: {len(tja_notes)} notes, {len(measures)} measures")
            
            return chart
            
        except Exception as e:
            self.logger.error(f"TJA generation failed: {e}")
            raise
    
    def _extract_note_predictions(self, model_outputs: Dict[str, Any]) -> np.ndarray:
        """Extract note predictions from model outputs"""
        if "logits" in model_outputs:
            logits = model_outputs["logits"]
            if torch.is_tensor(logits):
                logits = logits.cpu().numpy()
            
            # Apply softmax to get probabilities
            predictions = self._softmax(logits)
            
        elif "predictions" in model_outputs:
            predictions = model_outputs["predictions"]
            if torch.is_tensor(predictions):
                predictions = predictions.cpu().numpy()
        else:
            raise ValueError("No valid predictions found in model outputs")
        
        self.logger.debug(f"Extracted predictions shape: {predictions.shape}")
        return predictions
    
    def _softmax(self, x: np.ndarray) -> np.ndarray:
        """Apply softmax activation"""
        exp_x = np.exp(x - np.max(x, axis=-1, keepdims=True))
        return exp_x / np.sum(exp_x, axis=-1, keepdims=True)
    
    def _filter_predictions(self, predictions: np.ndarray, timing_grid: Dict) -> np.ndarray:
        """Apply thresholding and basic filtering"""
        # Apply threshold
        filtered = predictions.copy()
        
        # Set predictions below threshold to empty (class 0)
        max_probs = np.max(filtered, axis=-1)
        low_confidence_mask = max_probs < self.note_threshold
        
        filtered[low_confidence_mask] = 0
        filtered[low_confidence_mask, 0] = 1  # Set to empty note
        
        # Apply minimum gap constraint
        filtered = self._apply_minimum_gap(filtered, timing_grid)
        
        return filtered
    
    def _apply_minimum_gap(self, predictions: np.ndarray, timing_grid: Dict) -> np.ndarray:
        """Apply minimum gap constraint between notes"""
        times = timing_grid["times"]
        frame_rate = timing_grid["frame_rate"]
        min_gap_frames = int(self.minimum_note_gap * frame_rate)
        
        filtered = predictions.copy()
        
        # Find note positions (non-empty)
        note_positions = np.where(np.argmax(filtered, axis=-1) != 0)[0]
        
        # Remove notes that are too close
        if len(note_positions) > 1:
            to_remove = []
            for i in range(1, len(note_positions)):
                if note_positions[i] - note_positions[i-1] < min_gap_frames:
                    # Keep the note with higher confidence
                    curr_conf = np.max(filtered[note_positions[i]])
                    prev_conf = np.max(filtered[note_positions[i-1]])
                    
                    if curr_conf > prev_conf:
                        to_remove.append(note_positions[i-1])
                    else:
                        to_remove.append(note_positions[i])
            
            # Remove low-confidence notes
            for pos in to_remove:
                filtered[pos] = 0
                filtered[pos, 0] = 1  # Set to empty
        
        return filtered
    
    def _apply_density_smoothing(self, predictions: np.ndarray, difficulty_level: int) -> np.ndarray:
        """Apply density smoothing based on difficulty level"""
        # Define target densities (notes per second)
        target_densities = {8: 2.0, 9: 3.0, 10: 4.5}
        target_density = target_densities.get(difficulty_level, 3.0)
        
        # Calculate current density
        note_positions = np.where(np.argmax(predictions, axis=-1) != 0)[0]
        if len(note_positions) == 0:
            return predictions
        
        total_time = len(predictions) / 50.0  # Assuming 50 FPS
        current_density = len(note_positions) / total_time
        
        # Adjust density if needed
        if current_density > target_density * 1.5:
            # Too dense - remove some notes
            removal_ratio = 1.0 - (target_density / current_density)
            n_remove = int(len(note_positions) * removal_ratio)
            
            # Remove notes with lowest confidence
            confidences = [np.max(predictions[pos]) for pos in note_positions]
            remove_indices = np.argsort(confidences)[:n_remove]
            
            for idx in remove_indices:
                pos = note_positions[idx]
                predictions[pos] = 0
                predictions[pos, 0] = 1
        
        elif current_density < target_density * 0.5:
            # Too sparse - could add notes, but this is complex
            # For now, just log the issue
            self.logger.debug(f"Chart may be too sparse: {current_density:.1f} notes/sec")
        
        return predictions
    
    def _ensure_pattern_coherence(self, predictions: np.ndarray) -> np.ndarray:
        """Ensure musical pattern coherence"""
        # Simple pattern coherence: avoid isolated single notes
        note_types = np.argmax(predictions, axis=-1)
        
        for i in range(1, len(note_types) - 1):
            if note_types[i] != 0:  # If current position has a note
                # Check if it's isolated
                prev_empty = note_types[i-1] == 0
                next_empty = note_types[i+1] == 0
                
                # Look for nearby notes within a small window
                window = 5  # frames
                start = max(0, i - window)
                end = min(len(note_types), i + window + 1)
                
                nearby_notes = np.sum(note_types[start:end] != 0) - 1  # Exclude current note
                
                # If very isolated and low confidence, remove
                if nearby_notes == 0 and np.max(predictions[i]) < 0.7:
                    predictions[i] = 0
                    predictions[i, 0] = 1
        
        return predictions
    
    def _quantize_notes(self, predictions: np.ndarray, timing_grid: Dict) -> np.ndarray:
        """Quantize notes to beat grid"""
        beat_positions = timing_grid["beat_positions"]
        
        # Define quantization levels (fractions of a beat)
        quant_levels = [0, 0.25, 0.5, 0.75, 1.0]
        
        quantized = predictions.copy()
        note_positions = np.where(np.argmax(predictions, axis=-1) != 0)[0]
        
        for pos in note_positions:
            beat_pos = beat_positions[pos]
            beat_fraction = beat_pos % 1.0
            
            # Find closest quantization level
            closest_quant = min(quant_levels, key=lambda x: abs(x - beat_fraction))
            
            # Calculate new position
            new_beat_pos = int(beat_pos) + closest_quant
            
            # Find corresponding frame
            new_frame = np.argmin(np.abs(beat_positions - new_beat_pos))
            
            # Move note if different position
            if new_frame != pos and 0 <= new_frame < len(quantized):
                # Move note to quantized position
                quantized[new_frame] = predictions[pos]
                quantized[pos] = 0
                quantized[pos, 0] = 1
        
        return quantized
    
    def _create_tja_notes(self, predictions: np.ndarray, timing_grid: Dict) -> List[TJANote]:
        """Create TJANote objects from predictions"""
        times = timing_grid["times"]
        beat_positions = timing_grid["beat_positions"]
        
        tja_notes = []
        note_positions = np.where(np.argmax(predictions, axis=-1) != 0)[0]
        
        for pos in note_positions:
            note_type = np.argmax(predictions[pos])
            confidence = np.max(predictions[pos])
            
            # Map model output to TJA note types
            # Assuming model outputs: 0=empty, 1=don, 2=ka, 3=big_don, 4=big_ka, etc.
            tja_note_type = note_type
            
            note = TJANote(
                time=times[pos],
                note_type=tja_note_type,
                position=beat_positions[pos],
                measure=int(beat_positions[pos] // 4),
                confidence=confidence
            )
            
            tja_notes.append(note)
        
        # Sort by time
        tja_notes.sort(key=lambda x: x.time)
        
        return tja_notes
    
    def _generate_measures(self, notes: List[TJANote], timing_grid: Dict, bpm: float) -> List[str]:
        """Generate TJA measure strings"""
        if not notes:
            return ["0000,"]
        
        # Group notes by measure
        measures_dict = {}
        for note in notes:
            measure_num = note.measure
            if measure_num not in measures_dict:
                measures_dict[measure_num] = []
            measures_dict[measure_num].append(note)
        
        # Generate measure strings
        measures = []
        max_measure = max(measures_dict.keys()) if measures_dict else 0
        
        for measure_num in range(max_measure + 1):
            measure_notes = measures_dict.get(measure_num, [])
            
            # Create 16th note grid (16 positions per measure)
            measure_grid = [0] * 16
            
            for note in measure_notes:
                # Calculate position within measure (0-16)
                beat_in_measure = note.position % 4
                grid_pos = int(beat_in_measure * 4)  # 4 positions per beat
                grid_pos = min(15, max(0, grid_pos))  # Clamp to valid range
                
                measure_grid[grid_pos] = note.note_type
            
            # Convert to TJA string
            measure_str = ''.join(map(str, measure_grid)) + ','
            measures.append(measure_str)
        
        return measures
    
    def chart_to_tja_string(self, chart: TJAChart) -> str:
        """Convert TJAChart to complete TJA file string"""
        lines = []
        
        # Header
        lines.append(f"TITLE:{chart.title}")
        lines.append(f"ARTIST:{chart.artist}")
        lines.append(f"BPM:{chart.bpm}")
        lines.append(f"OFFSET:{chart.offset}")
        
        # Course information
        course_names = {"oni": "Oni", "edit": "Edit"}
        lines.append(f"COURSE:{course_names.get(chart.course_type, 'Oni')}")
        lines.append(f"LEVEL:{chart.difficulty_level}")
        
        # Additional metadata
        if chart.metadata:
            for key, value in chart.metadata.items():
                if key.upper() not in ["TITLE", "ARTIST", "BPM", "OFFSET", "COURSE", "LEVEL"]:
                    lines.append(f"{key.upper()}:{value}")
        
        lines.append("")  # Empty line before measures
        lines.append("#START")
        
        # Measures
        lines.extend(chart.measures)
        
        lines.append("#END")
        
        return '\n'.join(lines)

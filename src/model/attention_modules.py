"""
Attention Modules

Specialized attention mechanisms for TJA generation including cross-modal attention
between audio features and rhythmic patterns, and temporal attention for sequence modeling.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import math
from typing import Dict, Tuple, Optional


class MultiHeadAttention(nn.Module):
    """Multi-head attention with optional relative position encoding"""
    
    def __init__(self, d_model: int, num_heads: int, dropout: float = 0.1,
                 use_relative_position: bool = False, max_relative_distance: int = 32):
        super().__init__()
        
        assert d_model % num_heads == 0
        
        self.d_model = d_model
        self.num_heads = num_heads
        self.d_k = d_model // num_heads
        self.use_relative_position = use_relative_position
        self.max_relative_distance = max_relative_distance
        
        # Linear projections
        self.w_q = nn.Linear(d_model, d_model)
        self.w_k = nn.Linear(d_model, d_model)
        self.w_v = nn.Linear(d_model, d_model)
        self.w_o = nn.Linear(d_model, d_model)
        
        # Relative position embeddings (for temporal modeling)
        if use_relative_position:
            self.relative_position_k = nn.Parameter(
                torch.randn(2 * max_relative_distance + 1, self.d_k)
            )
            self.relative_position_v = nn.Parameter(
                torch.randn(2 * max_relative_distance + 1, self.d_k)
            )
        
        self.dropout = nn.Dropout(dropout)
        self.scale = math.sqrt(self.d_k)
        
    def _get_relative_positions(self, seq_len: int) -> torch.Tensor:
        """Generate relative position matrix"""
        positions = torch.arange(seq_len, dtype=torch.long)
        relative_positions = positions[:, None] - positions[None, :]
        
        # Clamp to max distance
        relative_positions = torch.clamp(
            relative_positions, 
            -self.max_relative_distance, 
            self.max_relative_distance
        )
        
        # Shift to positive indices
        relative_positions += self.max_relative_distance
        
        return relative_positions
    
    def forward(self, query: torch.Tensor, key: torch.Tensor, value: torch.Tensor,
                mask: Optional[torch.Tensor] = None) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Args:
            query: [batch, seq_len_q, d_model]
            key: [batch, seq_len_k, d_model]
            value: [batch, seq_len_v, d_model]
            mask: [batch, seq_len_q, seq_len_k] attention mask
            
        Returns:
            output: [batch, seq_len_q, d_model]
            attention_weights: [batch, num_heads, seq_len_q, seq_len_k]
        """
        batch_size, seq_len_q, _ = query.shape
        seq_len_k = key.shape[1]
        
        # Linear projections and reshape
        Q = self.w_q(query).view(batch_size, seq_len_q, self.num_heads, self.d_k).transpose(1, 2)
        K = self.w_k(key).view(batch_size, seq_len_k, self.num_heads, self.d_k).transpose(1, 2)
        V = self.w_v(value).view(batch_size, seq_len_k, self.num_heads, self.d_k).transpose(1, 2)
        
        # Scaled dot-product attention
        scores = torch.matmul(Q, K.transpose(-2, -1)) / self.scale
        
        # Add relative position bias if enabled
        if self.use_relative_position and seq_len_q == seq_len_k:
            relative_positions = self._get_relative_positions(seq_len_q).to(query.device)
            relative_k = self.relative_position_k[relative_positions]  # [seq_len_q, seq_len_k, d_k]
            
            # Compute relative position scores
            relative_scores = torch.einsum('bhqd,qkd->bhqk', Q, relative_k) / self.scale
            scores = scores + relative_scores
        
        # Apply attention mask
        if mask is not None:
            mask = mask.unsqueeze(1)  # [batch, 1, seq_len_q, seq_len_k]
            scores = scores.masked_fill(mask == 0, -1e9)
        
        # Softmax and dropout
        attention_weights = F.softmax(scores, dim=-1)
        attention_weights = self.dropout(attention_weights)
        
        # Apply attention to values
        context = torch.matmul(attention_weights, V)
        
        # Add relative position bias to values if enabled
        if self.use_relative_position and seq_len_q == seq_len_k:
            relative_v = self.relative_position_v[relative_positions]  # [seq_len_q, seq_len_k, d_k]
            relative_context = torch.einsum('bhqk,qkd->bhqd', attention_weights, relative_v)
            context = context + relative_context
        
        # Concatenate heads and project
        context = context.transpose(1, 2).contiguous().view(
            batch_size, seq_len_q, self.d_model
        )
        output = self.w_o(context)
        
        return output, attention_weights


class CrossModalAttention(nn.Module):
    """
    Cross-modal attention between audio features and rhythmic patterns
    Enables the model to align audio content with TJA note patterns
    """
    
    def __init__(self, d_model: int, num_heads: int = 8, dropout: float = 0.1):
        super().__init__()
        
        self.d_model = d_model
        self.num_heads = num_heads
        
        # Audio-to-rhythm attention
        self.audio_to_rhythm_attention = MultiHeadAttention(
            d_model, num_heads, dropout, use_relative_position=True
        )
        
        # Rhythm-to-audio attention  
        self.rhythm_to_audio_attention = MultiHeadAttention(
            d_model, num_heads, dropout, use_relative_position=True
        )
        
        # Feature fusion layers
        self.audio_norm = nn.LayerNorm(d_model)
        self.rhythm_norm = nn.LayerNorm(d_model)
        self.fusion_gate = nn.Linear(d_model * 2, d_model)
        self.output_projection = nn.Linear(d_model, d_model)
        
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, audio_features: torch.Tensor, rhythm_features: torch.Tensor,
                audio_mask: Optional[torch.Tensor] = None,
                rhythm_mask: Optional[torch.Tensor] = None) -> Dict[str, torch.Tensor]:
        """
        Cross-modal attention between audio and rhythm features
        
        Args:
            audio_features: [batch, time_audio, d_model] encoded audio features
            rhythm_features: [batch, time_rhythm, d_model] rhythm pattern features
            audio_mask: [batch, time_audio] audio attention mask
            rhythm_mask: [batch, time_rhythm] rhythm attention mask
            
        Returns:
            Dictionary containing fused features and attention weights
        """
        # Audio attending to rhythm patterns
        audio_to_rhythm, audio_rhythm_attn = self.audio_to_rhythm_attention(
            query=audio_features,
            key=rhythm_features,
            value=rhythm_features,
            mask=self._create_cross_mask(audio_mask, rhythm_mask)
        )
        
        # Rhythm attending to audio features
        rhythm_to_audio, rhythm_audio_attn = self.rhythm_to_audio_attention(
            query=rhythm_features,
            key=audio_features,
            value=audio_features,
            mask=self._create_cross_mask(rhythm_mask, audio_mask)
        )
        
        # Normalize attended features
        audio_attended = self.audio_norm(audio_features + self.dropout(audio_to_rhythm))
        rhythm_attended = self.rhythm_norm(rhythm_features + self.dropout(rhythm_to_audio))
        
        # Feature fusion with gating mechanism
        # Interpolate rhythm features to match audio sequence length
        if audio_features.shape[1] != rhythm_features.shape[1]:
            rhythm_attended = F.interpolate(
                rhythm_attended.transpose(1, 2),
                size=audio_features.shape[1],
                mode='linear',
                align_corners=False
            ).transpose(1, 2)
        
        # Concatenate and gate
        combined = torch.cat([audio_attended, rhythm_attended], dim=-1)
        gate_weights = torch.sigmoid(self.fusion_gate(combined))
        
        # Weighted combination
        fused_features = gate_weights * audio_attended + (1 - gate_weights) * rhythm_attended
        fused_features = self.output_projection(fused_features)
        
        return {
            "fused_features": fused_features,
            "audio_attended": audio_attended,
            "rhythm_attended": rhythm_attended,
            "audio_rhythm_attention": audio_rhythm_attn,
            "rhythm_audio_attention": rhythm_audio_attn,
            "gate_weights": gate_weights
        }
    
    def _create_cross_mask(self, query_mask: Optional[torch.Tensor],
                          key_mask: Optional[torch.Tensor]) -> Optional[torch.Tensor]:
        """Create cross-attention mask"""
        if query_mask is None or key_mask is None:
            return None
        
        # Create cross mask: [batch, seq_len_q, seq_len_k]
        cross_mask = query_mask.unsqueeze(-1) * key_mask.unsqueeze(-2)
        return cross_mask


class TemporalAttention(nn.Module):
    """
    Temporal attention for modeling long-range dependencies in TJA sequences
    Includes musical structure awareness (beats, measures, phrases)
    """
    
    def __init__(self, d_model: int, num_heads: int = 8, dropout: float = 0.1,
                 max_sequence_length: int = 2000):
        super().__init__()
        
        self.d_model = d_model
        self.num_heads = num_heads
        self.max_sequence_length = max_sequence_length
        
        # Multi-scale temporal attention
        self.local_attention = MultiHeadAttention(
            d_model, num_heads, dropout, 
            use_relative_position=True, max_relative_distance=16
        )
        
        self.global_attention = MultiHeadAttention(
            d_model, num_heads, dropout,
            use_relative_position=True, max_relative_distance=64
        )
        
        # Musical structure embeddings
        self.beat_embedding = nn.Embedding(4, d_model // 4)      # 4 beats per measure
        self.measure_embedding = nn.Embedding(32, d_model // 4)  # Up to 32 measures
        self.phrase_embedding = nn.Embedding(8, d_model // 4)    # Up to 8 phrases
        self.structure_projection = nn.Linear(d_model // 4 * 3, d_model)
        
        # Attention fusion
        self.attention_fusion = nn.Linear(d_model * 2, d_model)
        self.output_norm = nn.LayerNorm(d_model)
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, features: torch.Tensor, 
                beat_positions: Optional[torch.Tensor] = None,
                measure_positions: Optional[torch.Tensor] = None,
                phrase_positions: Optional[torch.Tensor] = None,
                attention_mask: Optional[torch.Tensor] = None) -> Dict[str, torch.Tensor]:
        """
        Temporal attention with musical structure awareness
        
        Args:
            features: [batch, time, d_model] input features
            beat_positions: [batch, time] beat position indices (0-3)
            measure_positions: [batch, time] measure position indices
            phrase_positions: [batch, time] phrase position indices
            attention_mask: [batch, time] attention mask
            
        Returns:
            Dictionary containing temporally attended features
        """
        batch_size, seq_len, _ = features.shape
        
        # Add musical structure embeddings if provided
        if beat_positions is not None or measure_positions is not None or phrase_positions is not None:
            structure_features = []
            
            if beat_positions is not None:
                beat_emb = self.beat_embedding(beat_positions.clamp(0, 3))
                structure_features.append(beat_emb)
            else:
                structure_features.append(torch.zeros(batch_size, seq_len, self.d_model // 4, 
                                                    device=features.device))
            
            if measure_positions is not None:
                measure_emb = self.measure_embedding(measure_positions.clamp(0, 31))
                structure_features.append(measure_emb)
            else:
                structure_features.append(torch.zeros(batch_size, seq_len, self.d_model // 4,
                                                    device=features.device))
            
            if phrase_positions is not None:
                phrase_emb = self.phrase_embedding(phrase_positions.clamp(0, 7))
                structure_features.append(phrase_emb)
            else:
                structure_features.append(torch.zeros(batch_size, seq_len, self.d_model // 4,
                                                    device=features.device))
            
            # Combine structure embeddings
            structure_combined = torch.cat(structure_features, dim=-1)
            structure_encoded = self.structure_projection(structure_combined)
            
            # Add to input features
            features = features + structure_encoded
        
        # Create attention mask for local and global attention
        if attention_mask is not None:
            # Convert to attention mask format
            attn_mask = attention_mask.unsqueeze(1) * attention_mask.unsqueeze(2)
        else:
            attn_mask = None
        
        # Local attention (short-range dependencies)
        local_attended, local_attn_weights = self.local_attention(
            features, features, features, attn_mask
        )
        
        # Global attention (long-range dependencies)
        global_attended, global_attn_weights = self.global_attention(
            features, features, features, attn_mask
        )
        
        # Fuse local and global attention
        combined_attention = torch.cat([local_attended, global_attended], dim=-1)
        fused_features = self.attention_fusion(combined_attention)
        
        # Residual connection and normalization
        output = self.output_norm(features + self.dropout(fused_features))
        
        return {
            "attended_features": output,
            "local_attention": local_attended,
            "global_attention": global_attended,
            "local_attention_weights": local_attn_weights,
            "global_attention_weights": global_attn_weights
        }

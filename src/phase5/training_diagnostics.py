"""
Training Diagnostics and Performance Profiling - Phase 5

Comprehensive training diagnostics, performance profiling, and monitoring
optimized for RTX 3070 hardware and TJA generation training.
"""

import torch
import torch.nn as nn
import torch.profiler
import time
import psutil
import numpy as np
from typing import Dict, List, Optional, Any, Tuple
import logging
from collections import defaultdict, deque
from dataclasses import dataclass
from pathlib import Path

# Optional imports
try:
    import GPUtil
    HAS_GPUTIL = True
except ImportError:
    HAS_GPUTIL = False

try:
    import matplotlib.pyplot as plt
    HAS_MATPLOTLIB = True
except ImportError:
    HAS_MATPLOTLIB = False

from .training_config import Phase5TrainingConfig
from ..utils.memory_monitor import MemoryMonitor


@dataclass
class TrainingMetrics:
    """Container for training metrics"""
    step: int
    loss: float
    learning_rate: float
    gpu_memory_gb: float
    gpu_utilization: float
    cpu_utilization: float
    throughput_samples_per_sec: float
    gradient_norm: float
    timestamp: float


class TrainingDiagnostics:
    """
    Comprehensive training diagnostics and monitoring
    
    Tracks training progress, identifies issues, and provides
    actionable insights for optimization.
    """
    
    def __init__(self, config: Phase5TrainingConfig):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Metrics storage
        self.metrics_history = deque(maxlen=10000)  # Keep last 10k steps
        self.loss_components_history = defaultdict(lambda: deque(maxlen=1000))
        self.gradient_stats_history = deque(maxlen=1000)
        
        # Performance tracking
        self.step_times = deque(maxlen=1000)
        self.memory_usage_history = deque(maxlen=1000)
        
        # Diagnostic flags
        self.issues_detected = []
        self.warnings_issued = set()
        
        # Memory monitor
        self.memory_monitor = MemoryMonitor()
        
        self.logger.info("Training diagnostics initialized")
    
    def record_step_metrics(self, 
                           step: int,
                           loss: float,
                           loss_components: Dict[str, float],
                           learning_rate: float,
                           gradient_norm: float,
                           step_time: float):
        """Record metrics for a training step"""
        # Get system metrics
        memory_stats = self.memory_monitor.get_memory_stats()
        
        # Create metrics record
        metrics = TrainingMetrics(
            step=step,
            loss=loss,
            learning_rate=learning_rate,
            gpu_memory_gb=memory_stats.gpu_reserved_gb,
            gpu_utilization=memory_stats.gpu_utilization_percent,
            cpu_utilization=psutil.cpu_percent(),
            throughput_samples_per_sec=self.config.get_effective_batch_size() / step_time,
            gradient_norm=gradient_norm,
            timestamp=time.time()
        )
        
        # Store metrics
        self.metrics_history.append(metrics)
        self.step_times.append(step_time)
        
        # Store loss components
        for component, value in loss_components.items():
            self.loss_components_history[component].append(value)
        
        # Run diagnostics
        self._run_diagnostics(metrics, loss_components)
    
    def _run_diagnostics(self, metrics: TrainingMetrics, loss_components: Dict[str, float]):
        """Run diagnostic checks on current metrics"""
        # Check for training issues
        self._check_loss_explosion(metrics)
        self._check_loss_stagnation()
        self._check_gradient_issues(metrics)
        self._check_memory_issues(metrics)
        self._check_performance_issues(metrics)
        self._check_loss_component_balance(loss_components)
    
    def _check_loss_explosion(self, metrics: TrainingMetrics):
        """Check for loss explosion"""
        if len(self.metrics_history) < 10:
            return
        
        recent_losses = [m.loss for m in list(self.metrics_history)[-10:]]
        
        # Check for sudden loss increase
        if metrics.loss > 10 * np.mean(recent_losses[:-1]):
            issue = f"Loss explosion detected at step {metrics.step}: {metrics.loss:.4f}"
            self._report_issue("loss_explosion", issue)
    
    def _check_loss_stagnation(self):
        """Check for loss stagnation"""
        if len(self.metrics_history) < 100:
            return
        
        recent_losses = [m.loss for m in list(self.metrics_history)[-100:]]
        
        # Check if loss hasn't improved significantly
        early_mean = np.mean(recent_losses[:20])
        late_mean = np.mean(recent_losses[-20:])
        
        improvement_ratio = (early_mean - late_mean) / early_mean
        
        if improvement_ratio < 0.01:  # Less than 1% improvement
            issue = f"Loss stagnation detected: {improvement_ratio*100:.2f}% improvement over 100 steps"
            self._report_issue("loss_stagnation", issue, severity="warning")
    
    def _check_gradient_issues(self, metrics: TrainingMetrics):
        """Check for gradient-related issues"""
        # Check for vanishing gradients
        if metrics.gradient_norm < 1e-6:
            issue = f"Vanishing gradients detected at step {metrics.step}: norm={metrics.gradient_norm:.2e}"
            self._report_issue("vanishing_gradients", issue)
        
        # Check for exploding gradients
        if metrics.gradient_norm > 100:
            issue = f"Exploding gradients detected at step {metrics.step}: norm={metrics.gradient_norm:.2e}"
            self._report_issue("exploding_gradients", issue)
    
    def _check_memory_issues(self, metrics: TrainingMetrics):
        """Check for memory-related issues"""
        # Check GPU memory usage
        if metrics.gpu_memory_gb > 7.0:  # Close to RTX 3070 limit
            issue = f"High GPU memory usage at step {metrics.step}: {metrics.gpu_memory_gb:.2f}GB"
            self._report_issue("high_gpu_memory", issue, severity="warning")
        
        # Check for memory leaks
        if len(self.memory_usage_history) >= 100:
            recent_memory = list(self.memory_usage_history)[-100:]
            memory_trend = np.polyfit(range(len(recent_memory)), recent_memory, 1)[0]
            
            if memory_trend > 0.01:  # Increasing by >10MB per step
                issue = f"Potential memory leak detected: {memory_trend*1000:.1f}MB/step increase"
                self._report_issue("memory_leak", issue, severity="warning")
        
        self.memory_usage_history.append(metrics.gpu_memory_gb)
    
    def _check_performance_issues(self, metrics: TrainingMetrics):
        """Check for performance issues"""
        # Check throughput
        if metrics.throughput_samples_per_sec < 1.0:  # Very slow training
            issue = f"Low training throughput at step {metrics.step}: {metrics.throughput_samples_per_sec:.2f} samples/sec"
            self._report_issue("low_throughput", issue, severity="warning")
        
        # Check GPU utilization
        if metrics.gpu_utilization < 50:  # Low GPU usage
            issue = f"Low GPU utilization at step {metrics.step}: {metrics.gpu_utilization:.1f}%"
            self._report_issue("low_gpu_utilization", issue, severity="warning")
    
    def _check_loss_component_balance(self, loss_components: Dict[str, float]):
        """Check if loss components are balanced"""
        if len(loss_components) < 2:
            return
        
        values = list(loss_components.values())
        max_val = max(values)
        min_val = min(values)
        
        # Check if one component dominates
        if max_val > 10 * min_val:
            dominant_component = max(loss_components.items(), key=lambda x: x[1])
            issue = f"Loss component imbalance: {dominant_component[0]} dominates with {dominant_component[1]:.4f}"
            self._report_issue("loss_imbalance", issue, severity="warning")
    
    def _report_issue(self, issue_type: str, message: str, severity: str = "error"):
        """Report a diagnostic issue"""
        issue_key = f"{issue_type}_{severity}"
        
        # Avoid spam by only reporting each issue type once per session
        if issue_key not in self.warnings_issued:
            self.warnings_issued.add(issue_key)
            
            if severity == "error":
                self.logger.error(f"DIAGNOSTIC: {message}")
            else:
                self.logger.warning(f"DIAGNOSTIC: {message}")
            
            self.issues_detected.append({
                "type": issue_type,
                "message": message,
                "severity": severity,
                "timestamp": time.time()
            })
    
    def get_training_summary(self) -> Dict[str, Any]:
        """Get comprehensive training summary"""
        if not self.metrics_history:
            return {"error": "No metrics recorded"}
        
        metrics_list = list(self.metrics_history)
        
        # Basic statistics
        losses = [m.loss for m in metrics_list]
        throughputs = [m.throughput_samples_per_sec for m in metrics_list]
        gpu_memory = [m.gpu_memory_gb for m in metrics_list]
        
        summary = {
            "training_progress": {
                "total_steps": len(metrics_list),
                "current_loss": losses[-1] if losses else 0.0,
                "best_loss": min(losses) if losses else 0.0,
                "loss_improvement": (losses[0] - losses[-1]) / losses[0] if len(losses) > 1 else 0.0
            },
            "performance": {
                "avg_throughput": np.mean(throughputs) if throughputs else 0.0,
                "peak_throughput": max(throughputs) if throughputs else 0.0,
                "avg_step_time": np.mean(self.step_times) if self.step_times else 0.0
            },
            "resource_usage": {
                "avg_gpu_memory_gb": np.mean(gpu_memory) if gpu_memory else 0.0,
                "peak_gpu_memory_gb": max(gpu_memory) if gpu_memory else 0.0,
                "memory_efficiency": (np.mean(gpu_memory) / 8.0) * 100 if gpu_memory else 0.0  # RTX 3070 has 8GB
            },
            "issues_detected": self.issues_detected,
            "recommendations": self._generate_recommendations()
        }
        
        return summary
    
    def _generate_recommendations(self) -> List[str]:
        """Generate training recommendations based on diagnostics"""
        recommendations = []
        
        if not self.metrics_history:
            return recommendations
        
        metrics_list = list(self.metrics_history)
        
        # Performance recommendations
        avg_throughput = np.mean([m.throughput_samples_per_sec for m in metrics_list])
        if avg_throughput < 2.0:
            recommendations.append("Consider increasing batch size or enabling mixed precision to improve throughput")
        
        # Memory recommendations
        avg_memory = np.mean([m.gpu_memory_gb for m in metrics_list])
        if avg_memory < 4.0:  # Using less than half of RTX 3070 memory
            recommendations.append("GPU memory usage is low - consider increasing batch size or model size")
        elif avg_memory > 7.0:
            recommendations.append("GPU memory usage is high - consider reducing batch size or enabling gradient checkpointing")
        
        # Loss recommendations
        if any(issue["type"] == "loss_stagnation" for issue in self.issues_detected):
            recommendations.append("Loss stagnation detected - consider adjusting learning rate or adding regularization")
        
        if any(issue["type"] == "loss_imbalance" for issue in self.issues_detected):
            recommendations.append("Loss component imbalance detected - consider adjusting loss weights")
        
        return recommendations
    
    def get_final_diagnostics(self) -> Dict[str, Any]:
        """Get final diagnostic report"""
        summary = self.get_training_summary()
        
        # Add detailed analysis
        final_diagnostics = {
            "summary": summary,
            "detailed_analysis": {
                "loss_analysis": self._analyze_loss_trends(),
                "performance_analysis": self._analyze_performance_trends(),
                "resource_analysis": self._analyze_resource_usage()
            },
            "optimization_suggestions": self._generate_optimization_suggestions()
        }
        
        return final_diagnostics
    
    def _analyze_loss_trends(self) -> Dict[str, Any]:
        """Analyze loss trends over training"""
        if not self.metrics_history:
            return {}
        
        losses = [m.loss for m in self.metrics_history]
        steps = [m.step for m in self.metrics_history]
        
        # Fit trend line
        if len(losses) > 10:
            trend_coeff = np.polyfit(steps, losses, 1)[0]
            
            return {
                "trend_slope": trend_coeff,
                "is_decreasing": trend_coeff < 0,
                "convergence_rate": abs(trend_coeff),
                "final_loss": losses[-1],
                "best_loss": min(losses),
                "loss_variance": np.var(losses[-100:]) if len(losses) > 100 else np.var(losses)
            }
        
        return {"insufficient_data": True}
    
    def _analyze_performance_trends(self) -> Dict[str, Any]:
        """Analyze performance trends"""
        if not self.metrics_history:
            return {}
        
        throughputs = [m.throughput_samples_per_sec for m in self.metrics_history]
        
        return {
            "avg_throughput": np.mean(throughputs),
            "throughput_stability": 1.0 - (np.std(throughputs) / np.mean(throughputs)) if throughputs else 0.0,
            "peak_throughput": max(throughputs) if throughputs else 0.0,
            "throughput_trend": np.polyfit(range(len(throughputs)), throughputs, 1)[0] if len(throughputs) > 10 else 0.0
        }
    
    def _analyze_resource_usage(self) -> Dict[str, Any]:
        """Analyze resource usage patterns"""
        if not self.metrics_history:
            return {}
        
        gpu_memory = [m.gpu_memory_gb for m in self.metrics_history]
        gpu_util = [m.gpu_utilization for m in self.metrics_history]
        
        return {
            "memory_efficiency": (np.mean(gpu_memory) / 8.0) * 100,  # RTX 3070 has 8GB
            "memory_stability": 1.0 - (np.std(gpu_memory) / np.mean(gpu_memory)) if gpu_memory else 0.0,
            "avg_gpu_utilization": np.mean(gpu_util) if gpu_util else 0.0,
            "gpu_utilization_stability": 1.0 - (np.std(gpu_util) / np.mean(gpu_util)) if gpu_util else 0.0
        }
    
    def _generate_optimization_suggestions(self) -> List[str]:
        """Generate specific optimization suggestions"""
        suggestions = []
        
        if not self.metrics_history:
            return suggestions
        
        # Analyze current state
        final_diagnostics = self._analyze_loss_trends()
        performance_analysis = self._analyze_performance_trends()
        resource_analysis = self._analyze_resource_usage()
        
        # Loss-based suggestions
        if final_diagnostics.get("convergence_rate", 0) < 1e-6:
            suggestions.append("Very slow convergence - consider increasing learning rate or using learning rate scheduling")
        
        if final_diagnostics.get("loss_variance", 0) > 0.1:
            suggestions.append("High loss variance - consider reducing learning rate or increasing batch size")
        
        # Performance-based suggestions
        if performance_analysis.get("avg_throughput", 0) < 1.0:
            suggestions.append("Low throughput - enable mixed precision training and increase batch size")
        
        if performance_analysis.get("throughput_stability", 1.0) < 0.8:
            suggestions.append("Unstable throughput - check for data loading bottlenecks")
        
        # Resource-based suggestions
        if resource_analysis.get("memory_efficiency", 0) < 50:
            suggestions.append("Low memory utilization - increase batch size or model complexity")
        
        if resource_analysis.get("avg_gpu_utilization", 0) < 70:
            suggestions.append("Low GPU utilization - optimize data loading and preprocessing")
        
        return suggestions


class PerformanceProfiler:
    """
    Advanced performance profiler for TJA training
    
    Uses PyTorch profiler to identify performance bottlenecks
    and optimization opportunities.
    """
    
    def __init__(self, config: Phase5TrainingConfig):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Profiling results
        self.profiling_results = []
        self.bottlenecks_identified = []
        
        self.logger.info("Performance profiler initialized")
    
    def profile_step(self, model: nn.Module, batch: Dict[str, torch.Tensor]):
        """Profile a single training step"""
        try:
            with torch.profiler.profile(
                activities=[
                    torch.profiler.ProfilerActivity.CPU,
                    torch.profiler.ProfilerActivity.CUDA,
                ],
                record_shapes=True,
                profile_memory=True,
                with_stack=True
            ) as prof:
                # Forward pass
                with torch.cuda.amp.autocast(enabled=self.config.mixed_precision):
                    outputs = model(
                        audio_features=batch["audio_features"],
                        target_sequence=batch.get("note_sequence"),
                        difficulty=batch["difficulty"],
                        return_loss=True
                    )
                
                # Backward pass
                loss = sum(outputs["losses"].values())
                loss.backward()
            
            # Analyze profiling results
            self._analyze_profiling_results(prof)
            
        except Exception as e:
            self.logger.warning(f"Profiling failed: {e}")
    
    def _analyze_profiling_results(self, prof: torch.profiler.profile):
        """Analyze profiling results and identify bottlenecks"""
        try:
            # Get key averages
            key_averages = prof.key_averages()
            
            # Find top time-consuming operations
            cpu_time_ops = sorted(key_averages, key=lambda x: x.cpu_time_total, reverse=True)[:10]
            cuda_time_ops = sorted(key_averages, key=lambda x: x.cuda_time_total, reverse=True)[:10]
            
            # Store results
            profiling_result = {
                "timestamp": time.time(),
                "top_cpu_ops": [(op.key, op.cpu_time_total) for op in cpu_time_ops],
                "top_cuda_ops": [(op.key, op.cuda_time_total) for op in cuda_time_ops],
                "total_cpu_time": sum(op.cpu_time_total for op in key_averages),
                "total_cuda_time": sum(op.cuda_time_total for op in key_averages)
            }
            
            self.profiling_results.append(profiling_result)
            
            # Identify bottlenecks
            self._identify_bottlenecks(profiling_result)
            
        except Exception as e:
            self.logger.warning(f"Profiling analysis failed: {e}")
    
    def _identify_bottlenecks(self, profiling_result: Dict[str, Any]):
        """Identify performance bottlenecks from profiling data"""
        # Check for data loading bottlenecks
        data_ops = [op for op in profiling_result["top_cpu_ops"] if "DataLoader" in op[0] or "collate" in op[0]]
        if data_ops and data_ops[0][1] > profiling_result["total_cpu_time"] * 0.3:
            self.bottlenecks_identified.append("Data loading bottleneck detected")
        
        # Check for memory transfer bottlenecks
        transfer_ops = [op for op in profiling_result["top_cuda_ops"] if "copy" in op[0].lower() or "transfer" in op[0].lower()]
        if transfer_ops and transfer_ops[0][1] > profiling_result["total_cuda_time"] * 0.2:
            self.bottlenecks_identified.append("Memory transfer bottleneck detected")
        
        # Check for attention bottlenecks
        attention_ops = [op for op in profiling_result["top_cuda_ops"] if "attention" in op[0].lower() or "bmm" in op[0].lower()]
        if attention_ops and attention_ops[0][1] > profiling_result["total_cuda_time"] * 0.4:
            self.bottlenecks_identified.append("Attention computation bottleneck detected")
    
    def get_profiling_summary(self) -> Dict[str, Any]:
        """Get comprehensive profiling summary"""
        if not self.profiling_results:
            return {"error": "No profiling data available"}
        
        # Aggregate results
        total_cpu_time = sum(result["total_cpu_time"] for result in self.profiling_results)
        total_cuda_time = sum(result["total_cuda_time"] for result in self.profiling_results)
        
        # Find most common bottlenecks
        bottleneck_counts = {}
        for bottleneck in self.bottlenecks_identified:
            bottleneck_counts[bottleneck] = bottleneck_counts.get(bottleneck, 0) + 1
        
        return {
            "profiling_sessions": len(self.profiling_results),
            "total_cpu_time_ms": total_cpu_time / 1000,  # Convert to ms
            "total_cuda_time_ms": total_cuda_time / 1000,
            "avg_cpu_time_per_step": total_cpu_time / len(self.profiling_results) / 1000,
            "avg_cuda_time_per_step": total_cuda_time / len(self.profiling_results) / 1000,
            "bottlenecks_identified": bottleneck_counts,
            "optimization_recommendations": self._generate_profiling_recommendations()
        }
    
    def _generate_profiling_recommendations(self) -> List[str]:
        """Generate optimization recommendations based on profiling"""
        recommendations = []
        
        # Check bottleneck patterns
        if "Data loading bottleneck detected" in self.bottlenecks_identified:
            recommendations.append("Optimize data loading: increase num_workers, use pin_memory=True, or preprocess data")
        
        if "Memory transfer bottleneck detected" in self.bottlenecks_identified:
            recommendations.append("Reduce memory transfers: keep data on GPU, use non_blocking=True for transfers")
        
        if "Attention computation bottleneck detected" in self.bottlenecks_identified:
            recommendations.append("Optimize attention: use flash attention, reduce sequence length, or optimize attention patterns")
        
        return recommendations

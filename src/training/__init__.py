"""
Training Module - Phase 3: Model Training and Optimization

Training pipeline for TJA generation model including data loading, training loops,
validation, and model optimization for RTX 3070 hardware.
"""

from .data_loader import T<PERSON><PERSON><PERSON><PERSON>oa<PERSON>, TJADataset
from .trainer import T<PERSON><PERSON>rainer
from .optimizer import create_optimizer, create_scheduler
from .metrics import TJAMetrics, GenerationEvaluator

__all__ = [
    'TJADataLoader',
    'TJADataset', 
    'TJATrainer',
    'create_optimizer',
    'create_scheduler',
    'TJAMetrics',
    'GenerationEvaluator'
]

# Phase 3 training configuration optimized for RTX 3070
PHASE_3_TRAINING_CONFIG = {
    "data": {
        "batch_size": 8,                    # Increased for better GPU utilization
        "gradient_accumulation_steps": 2,   # Adjusted for effective batch size 16
        "num_workers": 4,                   # Increased for better data loading
        "pin_memory": True,                 # Enabled for faster GPU transfer
        "train_split": 0.7,                 # 70% training
        "val_split": 0.15,                  # 15% validation
        "test_split": 0.15,                 # 15% testing
        "max_sequence_length": 800,         # Increased to match model config
        "shuffle": True
    },
    "optimization": {
        "optimizer": "adamw",
        "learning_rate": 1e-4,
        "weight_decay": 0.01,
        "beta1": 0.9,
        "beta2": 0.999,
        "eps": 1e-8,
        "scheduler": "cosine_with_warmup",
        "warmup_steps": 1000,
        "max_steps": 50000,
        "gradient_clip_norm": 1.0
    },
    "training": {
        "max_epochs": 10,                  # Reduced for demo
        "early_stopping_patience": 5,     # Reduced for demo
        "validation_frequency": 10,       # Every 10 steps for demo
        "checkpoint_frequency": 20,       # Every 20 steps for demo
        "logging_frequency": 5,           # Every 5 steps for demo
        "mixed_precision": False,         # Disabled due to overflow issues
        "gradient_checkpointing": True    # Memory efficiency
    },
    "hardware": {
        "device": "cuda",
        "gpu_memory_fraction": 0.70,       # Reduced to 70% of 8GB VRAM for safety
        "dataloader_workers": 2,           # Reduced workers to save memory
        "pin_memory": False,               # Disabled to save memory
        "non_blocking": True
    },
    "evaluation": {
        "generation_samples": 10,          # Generate 10 samples for evaluation
        "beam_size": 5,                    # Beam search size
        "temperature": 1.0,                # Sampling temperature
        "top_k": 50,                       # Top-k sampling
        "top_p": 0.9,                      # Nucleus sampling
        "metrics": [
            "note_accuracy",
            "sequence_accuracy", 
            "difficulty_appropriateness",
            "pattern_coherence",
            "musical_structure"
        ]
    }
}

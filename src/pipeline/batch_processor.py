"""
Batch Processor

Hardware-optimized batch processing for RTX 3070 system.
Manages memory, GPU utilization, and parallel processing.
"""

import torch
import logging
import time
from typing import Dict, List, Tuple, Any, Optional
from concurrent.futures import ThreadPoolExecutor, as_completed
from pathlib import Path
import json

from ..audio_processing.feature_extractor import AudioFeatureExtractor
from ..audio_processing.gpu_optimizer import RTX3070Optimizer
from ..validation.feature_validator import FeatureValidator
from ..validation.alignment_validator import AlignmentValidator


class BatchProcessor:
    """Hardware-optimized batch processing for audio feature extraction"""
    
    def __init__(self, batch_size: int = 4, num_workers: int = 4):
        self.batch_size = batch_size
        self.num_workers = num_workers
        self.logger = logging.getLogger(__name__)
        
        # Initialize components
        self.feature_extractor = AudioFeatureExtractor()
        self.feature_validator = FeatureValidator()
        self.alignment_validator = AlignmentValidator()
        
        # Initialize GPU optimizer
        self.gpu_optimizer = None
        if torch.cuda.is_available():
            try:
                self.gpu_optimizer = RTX3070Optimizer()
                self.logger.info("GPU optimizer initialized for batch processing")
            except Exception as e:
                self.logger.warning(f"GPU optimizer initialization failed: {e}")
        
        # Processing statistics
        self.stats = {
            "total_processed": 0,
            "successful_extractions": 0,
            "failed_extractions": 0,
            "total_processing_time": 0.0,
            "average_processing_time": 0.0,
            "gpu_utilization_history": [],
            "memory_usage_history": []
        }
        
        self.logger.info(f"BatchProcessor initialized: batch_size={batch_size}, workers={num_workers}")
    
    def process_batch(self, song_batch: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Process a batch of songs for feature extraction
        
        Args:
            song_batch: List of song dictionaries from Phase 1 catalog
            
        Returns:
            List of processing results
        """
        batch_start_time = time.time()
        results = []
        
        # Optimize batch size based on available memory
        if self.gpu_optimizer:
            optimized_batch_size = self.gpu_optimizer.optimize_batch_size(
                self.batch_size, 
                sequence_length=1000  # Estimate
            )
            if optimized_batch_size != self.batch_size:
                self.logger.info(f"Adjusted batch size: {self.batch_size} -> {optimized_batch_size}")
                song_batch = song_batch[:optimized_batch_size]
        
        self.logger.info(f"Processing batch of {len(song_batch)} songs")
        
        # Process songs in parallel
        with ThreadPoolExecutor(max_workers=self.num_workers) as executor:
            # Submit all tasks
            future_to_song = {
                executor.submit(self._process_single_song, song): song
                for song in song_batch
            }
            
            # Collect results as they complete
            for future in as_completed(future_to_song):
                song = future_to_song[future]
                try:
                    result = future.result()
                    results.append(result)
                    
                    if result["success"]:
                        self.stats["successful_extractions"] += 1
                    else:
                        self.stats["failed_extractions"] += 1
                        
                except Exception as e:
                    self.logger.error(f"Error processing song {song.get('song_id', 'unknown')}: {e}")
                    results.append({
                        "song_id": song.get("song_id", "unknown"),
                        "success": False,
                        "error": str(e),
                        "processing_time": 0.0
                    })
                    self.stats["failed_extractions"] += 1
        
        # Update statistics
        batch_processing_time = time.time() - batch_start_time
        self.stats["total_processed"] += len(song_batch)
        self.stats["total_processing_time"] += batch_processing_time
        self.stats["average_processing_time"] = (
            self.stats["total_processing_time"] / self.stats["total_processed"]
            if self.stats["total_processed"] > 0 else 0.0
        )
        
        # GPU memory cleanup
        if self.gpu_optimizer:
            self.gpu_optimizer.cleanup_memory()
        
        self.logger.info(f"Batch completed in {batch_processing_time:.2f}s: "
                        f"{self.stats['successful_extractions']}/{len(song_batch)} successful")
        
        return results
    
    def _process_single_song(self, song: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a single song for feature extraction
        
        Args:
            song: Song dictionary from Phase 1 catalog
            
        Returns:
            Processing result dictionary
        """
        start_time = time.time()
        song_id = song.get("song_id", "unknown")
        
        try:
            # Get audio path
            audio_path = song.get("audio_path")
            if not audio_path or not Path(audio_path).exists():
                return {
                    "song_id": song_id,
                    "success": False,
                    "error": f"Audio file not found: {audio_path}",
                    "processing_time": time.time() - start_time
                }
            
            # Prepare TJA metadata for feature extraction
            tja_metadata = {
                "base_bpm": song.get("notation_metadata", {}).get("base_bpm", 120.0),
                "offset": song.get("notation_metadata", {}).get("offset", 0.0),
                "timing_commands": self._extract_timing_commands(song)
            }
            
            # Extract features
            features = self.feature_extractor.extract_features(audio_path, tja_metadata)
            
            # Validate features
            feature_validation = self.feature_validator.validate_complete_features(features)
            
            # Validate alignment (if note data available)
            alignment_validation = None
            note_timings = self._extract_note_timings(song)
            if note_timings and "timing_grid" in features:
                alignment_validation = self.alignment_validator.validate_feature_note_alignment(
                    features["combined_features"],
                    note_timings,
                    features["timing_grid"]
                )
            
            # Prepare result
            processing_time = time.time() - start_time
            
            result = {
                "song_id": song_id,
                "success": True,
                "features": {
                    "combined_features": features["combined_features"],
                    "feature_metadata": features["feature_metadata"],
                    "audio_metadata": features["audio_metadata"]
                },
                "validation": {
                    "feature_validation": {
                        "valid": feature_validation.valid,
                        "quality_score": feature_validation.quality_score,
                        "warnings": feature_validation.warnings,
                        "errors": feature_validation.errors
                    },
                    "alignment_validation": {
                        "valid": alignment_validation.valid if alignment_validation else True,
                        "alignment_accuracy": alignment_validation.alignment_accuracy if alignment_validation else 1.0,
                        "mean_error_ms": alignment_validation.mean_error_ms if alignment_validation else 0.0
                    } if alignment_validation else None
                },
                "processing_time": processing_time,
                "tja_metadata": tja_metadata
            }
            
            return result
            
        except Exception as e:
            processing_time = time.time() - start_time
            self.logger.error(f"Error processing song {song_id}: {e}")
            return {
                "song_id": song_id,
                "success": False,
                "error": str(e),
                "processing_time": processing_time
            }
    
    def _extract_timing_commands(self, song: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract timing commands from song data"""
        timing_commands = []
        
        # Extract from all difficulties
        difficulties = song.get("difficulties", {})
        for difficulty_name, difficulty_data in difficulties.items():
            notation_data = difficulty_data.get("notation_data", {})
            commands = notation_data.get("timing_commands", [])
            
            for cmd in commands:
                if isinstance(cmd, dict) and cmd.get("type") in ["BPMCHANGE", "MEASURE", "DELAY"]:
                    timing_commands.append(cmd)
        
        # Sort by timing
        timing_commands.sort(key=lambda x: x.get("timing_ms", 0.0))
        
        return timing_commands
    
    def _extract_note_timings(self, song: Dict[str, Any]) -> List[float]:
        """Extract note timings from song data"""
        note_timings = []
        
        # Extract from all difficulties
        difficulties = song.get("difficulties", {})
        for difficulty_name, difficulty_data in difficulties.items():
            notation_data = difficulty_data.get("notation_data", {})
            sequences = notation_data.get("note_sequences", [])
            
            for note in sequences:
                if isinstance(note, dict) and note.get("type") != "blank":
                    timing_ms = note.get("timing_ms", 0.0)
                    if timing_ms > 0:  # Only positive timings
                        note_timings.append(timing_ms)
        
        # Remove duplicates and sort
        note_timings = sorted(list(set(note_timings)))
        
        return note_timings
    
    def save_batch_results(self, results: List[Dict[str, Any]], 
                          output_dir: str, batch_id: int) -> str:
        """
        Save batch processing results to disk
        
        Args:
            results: List of processing results
            output_dir: Output directory path
            batch_id: Batch identifier
            
        Returns:
            Path to saved file
        """
        try:
            output_path = Path(output_dir)
            output_path.mkdir(parents=True, exist_ok=True)
            
            # Prepare batch data
            batch_data = {
                "batch_id": batch_id,
                "batch_size": len(results),
                "processing_timestamp": time.strftime("%Y-%m-%dT%H:%M:%SZ", time.gmtime()),
                "successful_extractions": sum(1 for r in results if r["success"]),
                "failed_extractions": sum(1 for r in results if not r["success"]),
                "results": results
            }
            
            # Save to file
            batch_file = output_path / f"batch_{batch_id:04d}.json"
            with open(batch_file, 'w', encoding='utf-8') as f:
                json.dump(batch_data, f, indent=2, ensure_ascii=False, default=self._json_serializer)
            
            self.logger.info(f"Batch {batch_id} results saved to {batch_file}")
            return str(batch_file)
            
        except Exception as e:
            self.logger.error(f"Error saving batch {batch_id} results: {e}")
            raise
    
    def _json_serializer(self, obj):
        """Custom JSON serializer for torch tensors and numpy arrays"""
        if isinstance(obj, torch.Tensor):
            return obj.cpu().numpy().tolist()
        elif hasattr(obj, 'tolist'):  # numpy arrays
            return obj.tolist()
        elif hasattr(obj, 'item'):  # numpy scalars
            return obj.item()
        raise TypeError(f"Object of type {type(obj)} is not JSON serializable")
    
    def get_processing_statistics(self) -> Dict[str, Any]:
        """Get comprehensive processing statistics"""
        stats = self.stats.copy()
        
        # Add GPU statistics if available
        if self.gpu_optimizer:
            gpu_stats = self.gpu_optimizer.get_optimization_recommendations()
            stats["gpu_optimization"] = gpu_stats
            
            # Add current GPU status
            memory_stats = self.gpu_optimizer.get_memory_stats()
            stats["current_gpu_memory"] = {
                "allocated_gb": memory_stats.allocated_gb,
                "utilization_percent": memory_stats.utilization_percent,
                "available_gb": memory_stats.available_gb
            }
        
        # Calculate success rate
        total_attempts = stats["successful_extractions"] + stats["failed_extractions"]
        stats["success_rate"] = (
            stats["successful_extractions"] / total_attempts
            if total_attempts > 0 else 0.0
        )
        
        # Calculate throughput
        if stats["total_processing_time"] > 0:
            stats["songs_per_minute"] = (stats["total_processed"] / stats["total_processing_time"]) * 60
        else:
            stats["songs_per_minute"] = 0.0
        
        return stats

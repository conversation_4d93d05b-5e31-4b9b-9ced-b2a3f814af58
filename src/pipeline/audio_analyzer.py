"""
Audio Analyzer - Main Phase 2 Processing Pipeline

Coordinates the complete Phase 2 audio feature extraction pipeline.
Processes Phase 1 catalog and generates Phase 3-ready feature tensors.
"""

import json
import time
import logging
from pathlib import Path
from typing import Dict, List, Optional, Any
import torch

from .batch_processor import BatchProcessor
from ..utils.hardware_monitor import ResourceMonitor, setup_hardware_optimized_processing


class AudioAnalyzer:
    """Main Phase 2 processing pipeline coordinator"""
    
    def __init__(self, config: Optional[Dict] = None):
        self.config = config or setup_hardware_optimized_processing()
        self.logger = self._setup_logging()
        self.resource_monitor = ResourceMonitor()
        
        # Initialize batch processor
        self.batch_processor = BatchProcessor(
            batch_size=self.config.get("batch_size", 4),
            num_workers=self.config.get("parallel_workers", 4)
        )
        
        # Processing statistics
        self.stats = {
            "total_songs": 0,
            "processed_songs": 0,
            "successful_extractions": 0,
            "failed_extractions": 0,
            "total_processing_time": 0.0,
            "average_processing_time": 0.0,
            "feature_extraction_success_rate": 0.0,
            "temporal_alignment_accuracy": 0.0
        }
        
        # Output paths
        self.output_dir = Path("data/processed/audio_features")
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        self.logger.info("AudioAnalyzer initialized for Phase 2 processing")
        self.logger.info(f"Configuration: {self.config}")
    
    def _setup_logging(self) -> logging.Logger:
        """Setup logging for the analyzer"""
        logger = logging.getLogger(__name__)
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            # Console handler
            console_handler = logging.StreamHandler()
            console_handler.setLevel(logging.INFO)
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            console_handler.setFormatter(formatter)
            logger.addHandler(console_handler)
            
            # File handler
            log_dir = Path("logs")
            log_dir.mkdir(exist_ok=True)
            file_handler = logging.FileHandler(log_dir / "audio_analyzer.log")
            file_handler.setLevel(logging.DEBUG)
            file_handler.setFormatter(formatter)
            logger.addHandler(file_handler)
        
        return logger
    
    def load_phase1_catalog(self, catalog_path: str = "data/processed/catalog.json") -> Dict[str, Any]:
        """
        Load Phase 1 catalog for processing
        
        Args:
            catalog_path: Path to Phase 1 catalog
            
        Returns:
            Loaded catalog dictionary
        """
        try:
            catalog_file = Path(catalog_path)
            if not catalog_file.exists():
                raise FileNotFoundError(f"Phase 1 catalog not found: {catalog_path}")
            
            with open(catalog_file, 'r', encoding='utf-8') as f:
                catalog = json.load(f)
            
            songs = catalog.get("songs", [])
            self.stats["total_songs"] = len(songs)
            
            self.logger.info(f"Loaded Phase 1 catalog: {len(songs)} songs")
            
            # Filter for Phase 2 eligible songs
            eligible_songs = [
                song for song in songs 
                if song.get("phase_2_ready", False) and song.get("audio_path")
            ]
            
            self.logger.info(f"Phase 2 eligible songs: {len(eligible_songs)}/{len(songs)}")
            
            return {
                "catalog_metadata": catalog.get("phase_metadata", {}),
                "songs": eligible_songs,
                "processing_statistics": catalog.get("processing_statistics", {}),
                "total_songs": len(songs),
                "eligible_songs": len(eligible_songs)
            }
            
        except Exception as e:
            self.logger.error(f"Error loading Phase 1 catalog: {e}")
            raise
    
    def process_audio_features(self, catalog: Dict[str, Any], 
                             test_mode: bool = False, test_count: int = 50) -> Dict[str, Any]:
        """
        Process audio features for all songs in catalog
        
        Args:
            catalog: Phase 1 catalog data
            test_mode: If True, process only a subset for testing
            test_count: Number of songs to process in test mode
            
        Returns:
            Processing results and statistics
        """
        songs = catalog["songs"]
        
        if test_mode:
            songs = songs[:test_count]
            self.logger.info(f"Test mode: Processing {len(songs)} songs")
        
        self.stats["total_songs"] = len(songs)
        start_time = time.time()
        
        # Process songs in batches
        batch_size = self.config.get("batch_size", 4)
        all_results = []
        batch_files = []
        
        self.logger.info(f"Starting audio feature extraction with batch size {batch_size}")
        
        for i in range(0, len(songs), batch_size):
            batch_songs = songs[i:i + batch_size]
            batch_id = i // batch_size + 1
            
            self.logger.info(f"Processing batch {batch_id}/{(len(songs) + batch_size - 1) // batch_size}")
            
            # Process batch
            batch_results = self.batch_processor.process_batch(batch_songs)
            all_results.extend(batch_results)
            
            # Save batch results
            batch_file = self.batch_processor.save_batch_results(
                batch_results, 
                str(self.output_dir / "batches"), 
                batch_id
            )
            batch_files.append(batch_file)
            
            # Update statistics
            self._update_statistics(batch_results)
            
            # Progress logging
            progress = (len(all_results) / len(songs)) * 100
            self.logger.info(f"Progress: {progress:.1f}% ({len(all_results)}/{len(songs)})")
            
            # Memory cleanup between batches
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
        
        # Finalize statistics
        self.stats["total_processing_time"] = time.time() - start_time
        self.stats["processed_songs"] = len(all_results)
        
        if self.stats["processed_songs"] > 0:
            self.stats["average_processing_time"] = (
                self.stats["total_processing_time"] / self.stats["processed_songs"]
            )
            self.stats["feature_extraction_success_rate"] = (
                self.stats["successful_extractions"] / self.stats["processed_songs"]
            )
        
        # Generate comprehensive results
        results = {
            "phase_metadata": {
                "phase": "2",
                "version": "1.0.0",
                "processing_timestamp": time.strftime("%Y-%m-%dT%H:%M:%SZ", time.gmtime()),
                "total_processing_time_seconds": self.stats["total_processing_time"],
                "hardware_optimized": True
            },
            "processing_results": all_results,
            "batch_files": batch_files,
            "statistics": self.stats,
            "hardware_performance": self.resource_monitor.get_performance_stats(),
            "batch_processor_stats": self.batch_processor.get_processing_statistics()
        }
        
        # Save comprehensive results
        self._save_comprehensive_results(results)
        
        return results
    
    def _update_statistics(self, batch_results: List[Dict[str, Any]]):
        """Update processing statistics from batch results"""
        for result in batch_results:
            if result["success"]:
                self.stats["successful_extractions"] += 1
                
                # Update alignment accuracy if available
                validation = result.get("validation", {})
                alignment_val = validation.get("alignment_validation")
                if alignment_val and alignment_val.get("alignment_accuracy") is not None:
                    current_accuracy = self.stats.get("temporal_alignment_accuracy", 0.0)
                    new_accuracy = alignment_val["alignment_accuracy"]
                    
                    # Running average
                    count = self.stats["successful_extractions"]
                    self.stats["temporal_alignment_accuracy"] = (
                        (current_accuracy * (count - 1) + new_accuracy) / count
                    )
            else:
                self.stats["failed_extractions"] += 1
    
    def _save_comprehensive_results(self, results: Dict[str, Any]):
        """Save comprehensive Phase 2 results"""
        try:
            # Save main results file
            results_file = self.output_dir / "phase2_results.json"
            
            # Prepare serializable results (remove tensor data)
            serializable_results = self._prepare_serializable_results(results)
            
            with open(results_file, 'w', encoding='utf-8') as f:
                json.dump(serializable_results, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"Phase 2 results saved to {results_file}")
            
            # Save feature tensors separately (more efficient)
            self._save_feature_tensors(results["processing_results"])
            
            # Save processing statistics
            stats_file = self.output_dir / "processing_statistics.json"
            with open(stats_file, 'w', encoding='utf-8') as f:
                json.dump({
                    "phase2_statistics": results["statistics"],
                    "hardware_performance": results["hardware_performance"],
                    "batch_processor_stats": results["batch_processor_stats"]
                }, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"Processing statistics saved to {stats_file}")
            
        except Exception as e:
            self.logger.error(f"Error saving comprehensive results: {e}")
            raise
    
    def _prepare_serializable_results(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """Prepare results for JSON serialization (remove tensors)"""
        serializable = results.copy()
        
        # Remove tensor data from processing results
        for result in serializable["processing_results"]:
            if "features" in result:
                features = result["features"]
                if "combined_features" in features:
                    # Replace tensor with metadata
                    tensor = features["combined_features"]
                    if isinstance(tensor, torch.Tensor):
                        features["combined_features"] = {
                            "tensor_shape": list(tensor.shape),
                            "tensor_dtype": str(tensor.dtype),
                            "tensor_device": str(tensor.device),
                            "saved_separately": True
                        }
        
        return serializable
    
    def _save_feature_tensors(self, processing_results: List[Dict[str, Any]]):
        """Save feature tensors separately for efficient storage"""
        try:
            features_dir = self.output_dir / "feature_tensors"
            features_dir.mkdir(exist_ok=True)
            
            successful_saves = 0
            
            for result in processing_results:
                if result["success"] and "features" in result:
                    song_id = result["song_id"]
                    features = result["features"]
                    
                    if "combined_features" in features:
                        tensor = features["combined_features"]
                        if isinstance(tensor, torch.Tensor):
                            # Save as PyTorch tensor file
                            tensor_file = features_dir / f"{song_id}_features.pt"
                            torch.save({
                                "features": tensor,
                                "metadata": features.get("feature_metadata", {}),
                                "audio_metadata": features.get("audio_metadata", {}),
                                "song_id": song_id
                            }, tensor_file)
                            
                            successful_saves += 1
            
            self.logger.info(f"Saved {successful_saves} feature tensors to {features_dir}")
            
        except Exception as e:
            self.logger.error(f"Error saving feature tensors: {e}")
            raise
    
    def generate_phase3_catalog(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate Phase 3 input catalog from Phase 2 results
        
        Args:
            results: Phase 2 processing results
            
        Returns:
            Phase 3 catalog dictionary
        """
        try:
            phase3_catalog = {
                "phase_metadata": {
                    "phase": "3_input",
                    "version": "1.0.0",
                    "generated_from_phase": "2",
                    "generation_timestamp": time.strftime("%Y-%m-%dT%H:%M:%SZ", time.gmtime()),
                    "feature_tensor_format": "[T, 201]",
                    "frame_rate": 50.0
                },
                "songs": [],
                "dataset_statistics": {
                    "total_songs": len(results["processing_results"]),
                    "successful_extractions": results["statistics"]["successful_extractions"],
                    "failed_extractions": results["statistics"]["failed_extractions"],
                    "feature_extraction_success_rate": results["statistics"]["feature_extraction_success_rate"],
                    "average_temporal_alignment_accuracy": results["statistics"].get("temporal_alignment_accuracy", 0.0)
                }
            }
            
            # Process each successful result
            for result in results["processing_results"]:
                if result["success"]:
                    song_entry = {
                        "song_id": result["song_id"],
                        "feature_tensor_path": f"data/processed/audio_features/feature_tensors/{result['song_id']}_features.pt",
                        "feature_metadata": result["features"]["feature_metadata"],
                        "audio_metadata": result["features"]["audio_metadata"],
                        "validation_status": {
                            "feature_quality_valid": result["validation"]["feature_validation"]["valid"],
                            "feature_quality_score": result["validation"]["feature_validation"]["quality_score"],
                            "temporal_alignment_valid": result["validation"]["alignment_validation"]["valid"] if result["validation"]["alignment_validation"] else True,
                            "temporal_alignment_accuracy": result["validation"]["alignment_validation"]["alignment_accuracy"] if result["validation"]["alignment_validation"] else 1.0
                        },
                        "processing_metadata": {
                            "processing_time": result["processing_time"],
                            "tja_metadata": result["tja_metadata"]
                        },
                        "phase3_ready": (
                            result["validation"]["feature_validation"]["valid"] and
                            (result["validation"]["alignment_validation"]["valid"] if result["validation"]["alignment_validation"] else True)
                        )
                    }
                    
                    phase3_catalog["songs"].append(song_entry)
            
            # Save Phase 3 catalog
            catalog_file = self.output_dir / "phase3_input_catalog.json"
            with open(catalog_file, 'w', encoding='utf-8') as f:
                json.dump(phase3_catalog, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"Phase 3 catalog generated: {catalog_file}")
            self.logger.info(f"Phase 3 ready songs: {sum(1 for s in phase3_catalog['songs'] if s['phase3_ready'])}")
            
            return phase3_catalog
            
        except Exception as e:
            self.logger.error(f"Error generating Phase 3 catalog: {e}")
            raise

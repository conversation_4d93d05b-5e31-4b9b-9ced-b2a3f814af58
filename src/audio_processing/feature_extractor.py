"""
Audio Feature Extractor

Main feature extraction pipeline that combines spectral, rhythmic, and temporal features
into the target [T, 201] tensor format for Phase 3 consumption.
"""

import torch
import torchaudio
import librosa
import numpy as np
import logging
from typing import Dict, Optional, Tuple, Any
from pathlib import Path
from dataclasses import dataclass

from .spectral_processor import SpectralProcessor, SpectralConfig
from .rhythmic_processor import RhythmicProcessor, RhythmicConfig
from .temporal_aligner import TemporalAligner, AlignmentConfig, TimingCommand
from .gpu_optimizer import RTX3070Optimizer


@dataclass
class FeatureExtractionConfig:
    """Configuration for complete feature extraction pipeline"""
    sample_rate: int = 44100
    frame_rate: float = 50.0
    hop_length: int = 882  # 44100/50
    
    # Target feature dimensions
    target_feature_dims: int = 201
    spectral_dims: int = 153  # 128 mel + 13 mfcc + 12 chroma
    rhythmic_dims: int = 32
    temporal_dims: int = 16
    
    # Audio preprocessing
    normalize_audio: bool = True
    trim_silence: bool = True
    
    # Quality thresholds
    min_duration_seconds: float = 30.0
    max_duration_seconds: float = 600.0  # 10 minutes max


class AudioFeatureExtractor:
    """Complete audio feature extraction pipeline"""
    
    def __init__(self, config: Optional[FeatureExtractionConfig] = None, 
                 device: Optional[torch.device] = None):
        self.config = config or FeatureExtractionConfig()
        self.device = device or torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.logger = logging.getLogger(__name__)
        
        # Initialize processors
        self.spectral_processor = SpectralProcessor(
            SpectralConfig(
                sample_rate=self.config.sample_rate,
                frame_rate=self.config.frame_rate,
                hop_length=self.config.hop_length
            ),
            device=self.device
        )
        
        self.rhythmic_processor = RhythmicProcessor(
            RhythmicConfig(
                sample_rate=self.config.sample_rate,
                frame_rate=self.config.frame_rate,
                hop_length=self.config.hop_length,
                rhythmic_feature_dims=self.config.rhythmic_dims
            )
        )
        
        self.temporal_aligner = TemporalAligner(AlignmentConfig(frame_rate=self.config.frame_rate))
        
        # Initialize GPU optimizer if available
        self.gpu_optimizer = None
        if torch.cuda.is_available():
            try:
                self.gpu_optimizer = RTX3070Optimizer()
                self.device = self.gpu_optimizer.get_device()
            except Exception as e:
                self.logger.warning(f"GPU optimizer initialization failed: {e}")
        
        self.logger.info(f"AudioFeatureExtractor initialized on {self.device}")
    
    def load_audio(self, audio_path: str) -> Tuple[torch.Tensor, Dict[str, Any]]:
        """
        Load and preprocess audio file
        
        Args:
            audio_path: Path to audio file
            
        Returns:
            Tuple of (audio_tensor, metadata)
        """
        try:
            # Load audio using torchaudio for better format support
            audio, original_sr = torchaudio.load(audio_path)
            
            # Convert to mono if stereo
            if audio.shape[0] > 1:
                audio = torch.mean(audio, dim=0, keepdim=True)
            
            # Resample if necessary
            if original_sr != self.config.sample_rate:
                resampler = torchaudio.transforms.Resample(
                    orig_freq=original_sr,
                    new_freq=self.config.sample_rate
                )
                audio = resampler(audio)
            
            # Convert to 1D tensor
            audio = audio.squeeze(0)
            
            # Normalize audio
            if self.config.normalize_audio:
                audio = audio / (torch.max(torch.abs(audio)) + 1e-8)
            
            # Trim silence
            if self.config.trim_silence:
                # Convert to numpy for librosa processing
                audio_np = audio.numpy()
                audio_trimmed, _ = librosa.effects.trim(
                    audio_np, 
                    top_db=30,  # 30dB threshold
                    frame_length=2048,
                    hop_length=512
                )
                audio = torch.from_numpy(audio_trimmed).float()
            
            # Validate duration
            duration = len(audio) / self.config.sample_rate
            if duration < self.config.min_duration_seconds:
                self.logger.warning(f"Audio too short: {duration:.1f}s < {self.config.min_duration_seconds}s")
            elif duration > self.config.max_duration_seconds:
                self.logger.warning(f"Audio too long: {duration:.1f}s > {self.config.max_duration_seconds}s")
                # Truncate to max duration
                max_samples = int(self.config.max_duration_seconds * self.config.sample_rate)
                audio = audio[:max_samples]
                duration = self.config.max_duration_seconds
            
            metadata = {
                "original_sample_rate": original_sr,
                "duration_seconds": duration,
                "num_samples": len(audio),
                "channels": "mono",
                "normalized": self.config.normalize_audio,
                "trimmed": self.config.trim_silence
            }
            
            return audio, metadata
            
        except Exception as e:
            self.logger.error(f"Error loading audio from {audio_path}: {e}")
            raise
    
    def extract_temporal_features(self, audio: torch.Tensor) -> torch.Tensor:
        """
        Extract temporal features (RMS energy, spectral statistics)
        
        Args:
            audio: Audio tensor
            
        Returns:
            Temporal features tensor [time, 16]
        """
        try:
            # Convert to numpy for librosa processing
            audio_np = audio.cpu().numpy()
            
            # RMS energy
            rms = librosa.feature.rms(
                y=audio_np,
                hop_length=self.config.hop_length,
                frame_length=2048
            )[0]
            
            # Spectral centroid
            spectral_centroid = librosa.feature.spectral_centroid(
                y=audio_np,
                sr=self.config.sample_rate,
                hop_length=self.config.hop_length
            )[0]
            
            # Spectral bandwidth
            spectral_bandwidth = librosa.feature.spectral_bandwidth(
                y=audio_np,
                sr=self.config.sample_rate,
                hop_length=self.config.hop_length
            )[0]
            
            # Spectral rolloff
            spectral_rolloff = librosa.feature.spectral_rolloff(
                y=audio_np,
                sr=self.config.sample_rate,
                hop_length=self.config.hop_length
            )[0]
            
            # Zero crossing rate
            zcr = librosa.feature.zero_crossing_rate(
                audio_np,
                hop_length=self.config.hop_length,
                frame_length=2048
            )[0]
            
            # Spectral contrast
            spectral_contrast = librosa.feature.spectral_contrast(
                y=audio_np,
                sr=self.config.sample_rate,
                hop_length=self.config.hop_length,
                n_bands=6
            )
            
            # Combine features
            min_length = min(
                len(rms), len(spectral_centroid), len(spectral_bandwidth),
                len(spectral_rolloff), len(zcr), spectral_contrast.shape[1]
            )
            
            temporal_features = np.zeros((min_length, self.config.temporal_dims))
            
            # Fill feature matrix [time, 16]
            temporal_features[:, 0] = rms[:min_length]
            temporal_features[:, 1] = spectral_centroid[:min_length]
            temporal_features[:, 2] = spectral_bandwidth[:min_length]
            temporal_features[:, 3] = spectral_rolloff[:min_length]
            temporal_features[:, 4] = zcr[:min_length]
            
            # Spectral contrast (6 bands)
            temporal_features[:, 5:11] = spectral_contrast[:6, :min_length].T
            
            # Additional statistical features
            # Rolling mean and std of RMS (5 dimensions)
            window_size = min(10, min_length // 4)
            if window_size > 1:
                rms_rolling_mean = np.convolve(rms[:min_length], np.ones(window_size)/window_size, mode='same')
                rms_rolling_std = np.array([
                    np.std(rms[max(0, i-window_size//2):min(min_length, i+window_size//2+1)])
                    for i in range(min_length)
                ])
                temporal_features[:, 11] = rms_rolling_mean
                temporal_features[:, 12] = rms_rolling_std
            
            # Spectral flux (computed earlier in rhythmic features, simplified version here)
            if min_length > 1:
                spectral_flux_simple = np.diff(spectral_centroid[:min_length], prepend=spectral_centroid[0])
                temporal_features[:, 13] = spectral_flux_simple
            
            # Spectral flatness
            spectral_flatness = librosa.feature.spectral_flatness(
                y=audio_np,
                hop_length=self.config.hop_length
            )[0]
            temporal_features[:, 14] = spectral_flatness[:min_length]
            
            # Tonnetz (harmonic network) - first component only
            tonnetz = librosa.feature.tonnetz(
                y=audio_np,
                sr=self.config.sample_rate,
                hop_length=self.config.hop_length
            )
            temporal_features[:, 15] = tonnetz[0, :min_length]
            
            return torch.from_numpy(temporal_features).float()
            
        except Exception as e:
            self.logger.error(f"Error extracting temporal features: {e}")
            # Return zero features as fallback
            n_frames = len(audio) // self.config.hop_length + 1
            return torch.zeros((n_frames, self.config.temporal_dims))
    
    def extract_features(self, audio_path: str, tja_metadata: Dict[str, Any]) -> Dict[str, torch.Tensor]:
        """
        Extract complete feature set for a single audio file
        
        Args:
            audio_path: Path to audio file
            tja_metadata: TJA metadata including BPM, offset, timing commands
            
        Returns:
            Dictionary containing all extracted features
        """
        try:
            # Load audio
            audio, audio_metadata = self.load_audio(audio_path)
            
            # Move audio to device
            audio = audio.to(self.device)
            
            # Extract spectral features
            spectral_features = self.spectral_processor.extract_all_spectral_features(audio)
            
            # Extract rhythmic features (CPU-based)
            audio_cpu = audio.cpu().numpy()
            rhythmic_features = self.rhythmic_processor.extract_all_rhythmic_features(audio_cpu)
            
            # Extract temporal features
            temporal_features = self.extract_temporal_features(audio)
            
            # Create timing grid
            timing_commands = self._parse_timing_commands(tja_metadata.get('timing_commands', []))
            timing_grid = self.temporal_aligner.create_timing_grid(
                audio_metadata["duration_seconds"],
                tja_metadata.get("base_bpm", 120.0),
                tja_metadata.get("offset", 0.0),
                timing_commands
            )
            
            # Ensure all features have the same time dimension
            min_time_frames = min(
                spectral_features["combined_spectral"].shape[0],
                rhythmic_features["rhythmic_features"].shape[0],
                temporal_features.shape[0],
                timing_grid["beat_grid"].shape[0]
            )
            
            # Trim all features to same length
            spectral_trimmed = spectral_features["combined_spectral"][:min_time_frames]
            rhythmic_trimmed = rhythmic_features["rhythmic_features"][:min_time_frames]
            temporal_trimmed = temporal_features[:min_time_frames]
            
            # Move to same device
            spectral_trimmed = spectral_trimmed.to(self.device)
            rhythmic_trimmed = rhythmic_trimmed.to(self.device)
            temporal_trimmed = temporal_trimmed.to(self.device)
            
            # Combine into target [T, 201] tensor
            combined_features = torch.cat([
                spectral_trimmed,  # [T, 153]
                rhythmic_trimmed,  # [T, 32]
                temporal_trimmed   # [T, 16]
            ], dim=1)  # [T, 201]
            
            # Validate feature dimensions
            if combined_features.shape[1] != self.config.target_feature_dims:
                self.logger.error(f"Feature dimension mismatch: {combined_features.shape[1]} != {self.config.target_feature_dims}")
                raise ValueError(f"Feature dimension mismatch")
            
            return {
                "combined_features": combined_features,  # [T, 201] - main output
                "spectral_features": spectral_trimmed,
                "rhythmic_features": rhythmic_trimmed,
                "temporal_features": temporal_trimmed,
                "timing_grid": timing_grid,
                "audio_metadata": audio_metadata,
                "feature_metadata": {
                    "time_frames": min_time_frames,
                    "frame_rate": self.config.frame_rate,
                    "feature_dims": combined_features.shape[1],
                    "spectral_dims": spectral_trimmed.shape[1],
                    "rhythmic_dims": rhythmic_trimmed.shape[1],
                    "temporal_dims": temporal_trimmed.shape[1]
                }
            }
            
        except Exception as e:
            self.logger.error(f"Error extracting features from {audio_path}: {e}")
            raise
    
    def _parse_timing_commands(self, timing_commands_data: list) -> list:
        """Parse timing commands from TJA metadata"""
        timing_commands = []
        
        for cmd_data in timing_commands_data:
            if isinstance(cmd_data, dict):
                timing_cmd = TimingCommand(
                    type=cmd_data.get("type", ""),
                    value=cmd_data.get("value"),
                    timing_ms=cmd_data.get("timing_ms", 0.0),
                    measure=cmd_data.get("measure", 0)
                )
                timing_commands.append(timing_cmd)
        
        return timing_commands

"""
Audio Processing Module - Phase 2: Audio Feature Extraction and Temporal Alignment

Hardware-optimized audio feature extraction pipeline for RTX 3070 system.
Extracts multi-scale audio features with precise temporal alignment for TJA generation.
"""

from .feature_extractor import AudioFeatureExtractor
from .spectral_processor import SpectralProcessor
from .rhythmic_processor import RhythmicProcessor
from .temporal_aligner import TemporalAligner
from .gpu_optimizer import RTX3070Optimizer

__all__ = [
    'AudioFeatureExtractor',
    'SpectralProcessor', 
    'RhythmicProcessor',
    'TemporalAligner',
    'RTX3070Optimizer'
]

# Phase 2 feature extraction configuration
PHASE_2_CONFIG = {
    "feature_extraction": {
        "frame_rate": 50.0,                    # 50 FPS for temporal resolution
        "hop_length": 882,                     # 44100/50 = 882 samples per frame
        "sample_rate": 44100,                  # Standard audio sample rate
        "feature_dimensions": {
            "mel_spectrogram": 128,            # 128 mel bins (80Hz-8kHz)
            "mfcc": 13,                        # 13 MFCC coefficients
            "chroma": 12,                      # 12 pitch classes
            "rhythmic": 32,                    # Onset, tempo, beat features
            "temporal": 16,                    # RMS, spectral statistics
            "total": 201                       # Combined feature channels
        }
    },
    "hardware_optimization": {
        "gpu_model": "RTX 3070",
        "max_vram_gb": 6.8,                    # 6.8GB of 8GB VRAM (safety margin)
        "batch_size": 4,                       # 4 songs per batch
        "parallel_workers": 4,                 # 4 audio processing workers
        "memory_monitoring": True,
        "automatic_cleanup": True
    },
    "quality_targets": {
        "temporal_alignment_accuracy": 0.95,   # 95% alignment accuracy
        "feature_extraction_success": 0.98,    # 98% extraction success
        "synchronization_accuracy": 0.97,      # 97% sync accuracy
        "processing_speed_target": 20          # 20 songs per minute minimum
    }
}
